#!/usr/bin/env python3
"""
Seamless Stone Wall Generator - Creates a natural dry stone wall that completely covers the canvas
"""

import numpy as np
import random
from PIL import Image
import sys
import os

# Import the original stone detection system
import importlib.util
spec = importlib.util.spec_from_file_location("stackStones4_Copy", "stackStones4 - Copy.py")
stackStones4_Copy = importlib.util.module_from_spec(spec)
spec.loader.exec_module(stackStones4_Copy)
StoneWallGenerator = stackStones4_Copy.StoneWallGenerator

class SeamlessStoneWallGenerator(StoneWallGenerator):
    """Enhanced stone wall generator with seamless placement algorithm"""
    
    def generate_seamless_wall(self, width=1000, height=800, bg_color=(200, 200, 200), visualize=False):
        """
        Generate a seamless dry stone wall that completely covers the canvas
        Following the algorithm requirements:
        1. Start at top-left corner
        2. Place stones edge-to-edge with natural variation
        3. Ensure no overlaps and maintain contact
        4. Fill gaps with smaller stones
        5. Complete canvas coverage
        """
        if not self.stones:
            self.extract_stones()

        valid_stones = [s for s in self.stones if self.is_valid_stone(s)]
        if len(valid_stones) == 0:
            print("No valid stones were extracted. Try adjusting the parameters.")
            return None

        print(f"Using {len(valid_stones)} detected stones for seamless wall generation")

        # Create the wall image with background color
        wall_image = Image.new('RGB', (width, height), bg_color)
        
        # Create placement grid to track occupied areas
        placement_grid = np.zeros((height, width), dtype=bool)
        
        # Track placed stones
        placed_stones = []
        stones_placed = 0
        
        # Create stone pool with rotations for better fitting
        stone_pool = self.create_stone_pool(valid_stones)
        print(f"Created stone pool with {len(stone_pool)} stones (including rotations)")
        
        # Algorithm: Start at top-left corner and fill systematically
        print("Starting seamless placement from top-left corner...")
        
        # Place first stone at top-left corner
        first_stone = self.find_best_stone_for_position(stone_pool, 0, 0, width, height)
        if first_stone:
            if self.place_stone_at_position(first_stone, 0, 0, wall_image, placement_grid):
                placed_stones.append({
                    'x': 0, 'y': 0, 
                    'width': first_stone['width'], 
                    'height': first_stone['height'],
                    'stone': first_stone
                })
                stones_placed += 1
                stone_pool.remove(first_stone)
                print("✓ Placed first stone at top-left corner")
        
        # Main placement loop - fill canvas systematically
        placement_queue = [(first_stone['width'], 0)]  # Start next to first stone
        
        while placement_queue and stone_pool:
            current_x, current_y = placement_queue.pop(0)
            
            # Skip if position is already occupied
            if (current_x >= width or current_y >= height or 
                (current_x < width and current_y < height and placement_grid[current_y, current_x])):
                continue
            
            # Find best fitting stone for current position
            best_stone = self.find_best_stone_for_position(
                stone_pool, current_x, current_y, width, height, placement_grid
            )
            
            if best_stone:
                # Apply random but constrained orientation
                oriented_stone = self.apply_natural_orientation(best_stone)
                
                # Place the stone
                if self.place_stone_with_contact_check(
                    oriented_stone, current_x, current_y, wall_image, placement_grid, placed_stones
                ):
                    placed_stones.append({
                        'x': current_x, 'y': current_y,
                        'width': oriented_stone['width'], 
                        'height': oriented_stone['height'],
                        'stone': oriented_stone
                    })
                    stones_placed += 1
                    stone_pool.remove(best_stone)
                    
                    # Add adjacent positions to placement queue
                    self.add_adjacent_positions(
                        placement_queue, current_x, current_y, 
                        oriented_stone['width'], oriented_stone['height'], width, height
                    )
            
            if stones_placed % 20 == 0:
                print(f"Placed {stones_placed} stones...")
        
        # Fill remaining gaps with smaller stones
        print("Filling remaining gaps...")
        gaps_filled = self.fill_remaining_gaps(stone_pool, wall_image, placement_grid, width, height)
        
        print(f"Seamless wall generation complete: {stones_placed} stones placed, {gaps_filled} gaps filled")
        
        # Convert to numpy array for return
        wall_array = np.array(wall_image)
        
        if visualize:
            try:
                import matplotlib.pyplot as plt
                plt.figure(figsize=(15, 10))
                plt.imshow(wall_array)
                plt.title("Seamless Dry Stone Wall (Complete Canvas Coverage)", fontsize=16)
                plt.axis('off')
                plt.tight_layout()
                plt.show()
            except ImportError:
                print("Matplotlib not available for visualization")
        
        return wall_array

    def create_stone_pool(self, valid_stones):
        """Create a pool of stones including rotated versions"""
        stone_pool = []
        
        for stone in valid_stones:
            # Add original stone
            stone_pool.append(stone)
            
            # Add rotated versions for better fitting
            for angle in [90, 180, 270]:
                try:
                    rotated_stone = self.rotate_stone(stone, angle)
                    if rotated_stone:
                        stone_pool.append(rotated_stone)
                except:
                    continue
        
        return stone_pool

    def rotate_stone(self, stone, angle):
        """Rotate a stone by the given angle"""
        try:
            stone_img = stone['image']
            
            if angle == 90:
                rotated_img = np.rot90(stone_img, k=1)
            elif angle == 180:
                rotated_img = np.rot90(stone_img, k=2)
            elif angle == 270:
                rotated_img = np.rot90(stone_img, k=3)
            else:
                return None
            
            return {
                'image': rotated_img,
                'width': rotated_img.shape[1],
                'height': rotated_img.shape[0],
                'area': rotated_img.shape[0] * rotated_img.shape[1],
                'rotation': angle
            }
        except:
            return None

    def find_best_stone_for_position(self, stone_pool, x, y, canvas_width, canvas_height, placement_grid=None):
        """Find the best stone that fits at the given position"""
        suitable_stones = []
        
        for stone in stone_pool:
            # Check if stone fits within canvas bounds
            if x + stone['width'] <= canvas_width and y + stone['height'] <= canvas_height:
                # Check if position is free
                if placement_grid is None or self.is_position_free(x, y, stone['width'], stone['height'], placement_grid):
                    suitable_stones.append(stone)
        
        if not suitable_stones:
            return None
        
        # Prefer larger stones for better coverage, but add randomness
        suitable_stones.sort(key=lambda s: s['area'], reverse=True)
        
        # Choose from top candidates with some randomness
        num_candidates = min(3, len(suitable_stones))
        return random.choice(suitable_stones[:num_candidates])

    def apply_natural_orientation(self, stone):
        """Apply random but constrained orientation to simulate natural variation"""
        # For now, return stone as-is (rotation already handled in stone pool)
        # Could add slight position adjustments here if needed
        return stone

    def place_stone_with_contact_check(self, stone, x, y, wall_image, placement_grid, placed_stones):
        """Place stone ensuring it has proper contact with existing stones"""
        # Check if stone has contact with existing stones (except for first stone)
        if placed_stones and not self.has_proper_contact(x, y, stone['width'], stone['height'], placement_grid):
            return False
        
        return self.place_stone_at_position(stone, x, y, wall_image, placement_grid)

    def is_position_free(self, x, y, width, height, placement_grid):
        """Check if the given position is free"""
        try:
            return not np.any(placement_grid[y:y+height, x:x+width])
        except:
            return False

    def has_proper_contact(self, x, y, width, height, placement_grid):
        """Check if stone has contact with existing stones"""
        # Check all edges for contact
        try:
            # Left edge
            if x > 0 and np.any(placement_grid[y:y+height, x-1]):
                return True
            # Top edge  
            if y > 0 and np.any(placement_grid[y-1, x:x+width]):
                return True
            # Right edge
            if x + width < placement_grid.shape[1] and np.any(placement_grid[y:y+height, x+width]):
                return True
            # Bottom edge
            if y + height < placement_grid.shape[0] and np.any(placement_grid[y+height, x:x+width]):
                return True
        except:
            pass
        
        return False

    def place_stone_at_position(self, stone, x, y, wall_image, placement_grid):
        """Place a stone at the specified position"""
        try:
            stone_img = stone['image']
            
            # Convert to PIL and place
            if stone_img.ndim == 4:
                stone_pil = Image.fromarray(stone_img)
                wall_image.paste(stone_pil, (x, y), stone_pil)
            else:
                stone_pil = Image.fromarray(stone_img).convert('RGB')
                wall_image.paste(stone_pil, (x, y))
            
            # Mark area as occupied
            placement_grid[y:y+stone['height'], x:x+stone['width']] = True
            return True
            
        except Exception as e:
            print(f"Error placing stone: {e}")
            return False

    def add_adjacent_positions(self, queue, x, y, width, height, canvas_width, canvas_height):
        """Add adjacent positions to the placement queue"""
        # Add positions to the right and below
        adjacent_positions = [
            (x + width, y),      # Right
            (x, y + height),     # Below
            (x + width, y + height)  # Diagonal
        ]
        
        for pos_x, pos_y in adjacent_positions:
            if pos_x < canvas_width and pos_y < canvas_height:
                if (pos_x, pos_y) not in queue:
                    queue.append((pos_x, pos_y))

    def fill_remaining_gaps(self, stone_pool, wall_image, placement_grid, width, height):
        """Fill remaining gaps with smaller stones"""
        gaps_filled = 0
        small_stones = [s for s in stone_pool if s['area'] < 2000]
        
        for y in range(0, height, 5):
            for x in range(0, width, 5):
                if not placement_grid[y, x]:  # Found empty spot
                    # Find suitable small stone
                    for stone in small_stones:
                        if (x + stone['width'] <= width and 
                            y + stone['height'] <= height and
                            self.is_position_free(x, y, stone['width'], stone['height'], placement_grid)):
                            
                            if self.place_stone_at_position(stone, x, y, wall_image, placement_grid):
                                gaps_filled += 1
                                small_stones.remove(stone)
                                break
        
        return gaps_filled

def test_seamless_wall():
    """Test the seamless stone wall generation"""
    print("Testing Seamless Stone Wall Generation")
    print("=" * 50)
    
    # Test with available stone images
    stone_images = ['stones1.jpg', 'stones2.jpg']
    
    for stone_image in stone_images:
        if os.path.exists(stone_image):
            print(f"\nTesting with {stone_image}...")
            
            try:
                # Create seamless generator
                generator = SeamlessStoneWallGenerator(stone_image)
                
                # Generate seamless wall
                output_path = f"seamless_wall_from_{stone_image.replace('.jpg', '.png')}"
                wall_array = generator.generate_seamless_wall(
                    width=1200, height=800, 
                    bg_color=(220, 215, 210), 
                    visualize=True
                )
                
                if wall_array is not None:
                    # Save result
                    wall_image = Image.fromarray(wall_array)
                    wall_image.save(output_path)
                    print(f"✓ Seamless wall saved to {output_path}")
                else:
                    print(f"✗ Failed to generate seamless wall from {stone_image}")
                    
            except Exception as e:
                print(f"✗ Error processing {stone_image}: {e}")
        else:
            print(f"! Stone image {stone_image} not found")

if __name__ == "__main__":
    test_seamless_wall()
