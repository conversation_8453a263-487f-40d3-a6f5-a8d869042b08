import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage import measure, filters, morphology, exposure
from skimage.segmentation import watershed, mark_boundaries
from skimage.feature import peak_local_max, canny
from scipy import ndimage
import random
from PIL import Image, ImageDraw, ImageFont
import os
import argparse
import sys
from itertools import product

class StoneWallGenerator:
    def __init__(self, image_path):
        """Initialize with the path to the stone wall image"""
        self.original_image = cv2.imread(image_path)
        if self.original_image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        # Convert to RGB for display purposes
        self.display_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
        
        # Will store the detected stones
        self.stones = []
        
        # Store image dimensions
        self.img_height, self.img_width = self.original_image.shape[:2]
        
        # Store preprocessed versions of the image
        self.preprocessed_images = {}

    def enhance_stone_boundaries(self, input_image, method='all'):

        """
        Enhanced boundary detection with advanced techniques
        
        Parameters:
        - input_image: Input image
        - method: Specific method to use or 'all' to try multiple methods
        
        Returns:
        Dictionary of enhanced images or a single enhanced image
        """
        def ensure_8bit_grayscale(image):
            """Ensure image is 8-bit grayscale"""
            if image.ndim > 2:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            if image.dtype != np.uint8:
                image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
            return image
        
        def safe_laplacian(image):
            """Apply Laplacian with noise reduction"""
            image = ensure_8bit_grayscale(image)
            # Denoise before Laplacian
            denoised = cv2.GaussianBlur(image, (3, 3), 0)
            # Apply Laplacian with larger kernel
            laplacian = cv2.Laplacian(denoised, cv2.CV_64F, ksize=5)
            # Convert back to 8-bit
            return cv2.normalize(laplacian, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Dictionary to store enhanced images
        enhanced = {}
        
        # Ensure input is 8-bit grayscale
        input_image = ensure_8bit_grayscale(input_image)
        
        # 1. Laplacian of Gaussian with stronger parameters
        enhanced['log'] = safe_laplacian(input_image)
        
        # 2. Difference of Gaussians with multiple scales
        def difference_of_gaussians(image, low_sigma=1, high_sigma=7):
            image = ensure_8bit_grayscale(image)
            low_blur = cv2.GaussianBlur(image, (0, 0), low_sigma)
            high_blur = cv2.GaussianBlur(image, (0, 0), high_sigma)
            dog = cv2.subtract(low_blur, high_blur)  # Note: low - high for edge enhancement
            return cv2.normalize(dog, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Apply DoG with multiple scales and combine
        dog1 = difference_of_gaussians(input_image, 1, 5)
        dog2 = difference_of_gaussians(input_image, 2, 7)
        dog3 = difference_of_gaussians(input_image, 3, 9)
        
        # Combine multiple DoG scales
        enhanced['dog'] = cv2.max(cv2.max(dog1, dog2), dog3)
        
        # 3. Sobel edge detection with gradient combination
        def sobel_edges(image):
            image = ensure_8bit_grayscale(image)
            # Apply Sobel in x and y directions with larger kernel
            sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=5)
            sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=5)
            # Calculate gradient magnitude
            magnitude = np.sqrt(sobelx**2 + sobely**2)
            # Normalize and convert to 8-bit
            return cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        enhanced['sobel'] = sobel_edges(input_image)
        
        # 4. Local Binary Pattern for texture-based edge detection
        def lbp_edges(image, radius=3):
            from skimage.feature import local_binary_pattern
            image = ensure_8bit_grayscale(image)
            # Number of points to consider
            n_points = 8 * radius
            # Compute LBP
            lbp = local_binary_pattern(image, n_points, radius, method='uniform')
            # Convert to 8-bit
            return cv2.normalize(lbp, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        try:
            enhanced['lbp'] = lbp_edges(input_image)
        except:
            # If skimage is not available
            enhanced['lbp'] = enhanced['sobel']
        
        # 5. Morphological gradient for edge detection
        def morphological_gradient(image):
            image = ensure_8bit_grayscale(image)
            kernel = np.ones((5, 5), np.uint8)
            # Apply morphological gradient
            return cv2.morphologyEx(image, cv2.MORPH_GRADIENT, kernel)
        
        enhanced['morph_gradient'] = morphological_gradient(input_image)
        
        # 6. Adaptive thresholding for local contrast enhancement
        def adaptive_threshold_edges(image):
            image = ensure_8bit_grayscale(image)
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY_INV, 21, 5
            )
            # Clean up with morphological operations
            kernel = np.ones((3, 3), np.uint8)
            return cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
        
        enhanced['adaptive_thresh'] = adaptive_threshold_edges(input_image)
        
        # 7. Canny edge detection with optimal thresholds
        def auto_canny(image, sigma=0.33):
            image = ensure_8bit_grayscale(image)
            # Compute median of the image
            v = np.median(image)
            # Apply automatic Canny edge detection using the median
            lower = int(max(0, (1.0 - sigma) * v))
            upper = int(min(255, (1.0 + sigma) * v))
            return cv2.Canny(image, lower, upper)
        
        enhanced['canny'] = auto_canny(input_image)
        
        # 8. Combined approach - weighted sum of multiple methods
        combined = np.zeros_like(input_image)
        weights = {
            'log': 0.15,
            'dog': 0.2,
            'sobel': 0.15,
            'lbp': 0.1,
            'morph_gradient': 0.15,
            'adaptive_thresh': 0.1,
            'canny': 0.15
        }
        
        for method_name, weight in weights.items():
            if method_name in enhanced:
                combined = cv2.addWeighted(combined, 1.0, enhanced[method_name], weight, 0)
        
        # Normalize the combined result
        enhanced['combined'] = cv2.normalize(combined, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Return the requested method or all methods
        if method != 'all' and method in enhanced:
            return enhanced[method]
        
        return enhanced
        
    def preprocess_image(self, visualize=False):
        """Extended preprocessing with saturation/highlight adjustment and boundary enhancement"""
        # Initialize preprocessed images dictionary
        preprocessed = {}
        
        # First, apply saturation and highlight adjustment
        adjusted_image = self.adjust_saturation_highlights(
            self.original_image, 
            saturation_factor=-100,  # Decrease saturation by 80
            highlight_factor=100     # Increase highlights by 80
        )
        
        # Store the adjusted image
        preprocessed['adjusted'] = cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2GRAY)
        
        # Use the adjusted image for further processing
        gray_adjusted = cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2GRAY)
        preprocessed['original'] = gray_adjusted
        
        # Also keep the original grayscale for comparison
        gray_original = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        preprocessed['original_unmodified'] = gray_original
        
        # Safe image combination function
        def safe_combine_images(img1, img2):
            """Safely combine two images of potentially different sizes"""
            # Ensure both images are grayscale and same type
            if img1.ndim > 2:
                img1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
            if img2.ndim > 2:
                img2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
            
            # Resize img2 to match img1 if sizes differ
            if img1.shape != img2.shape:
                img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]), 
                                interpolation=cv2.INTER_LINEAR)
            
            # Ensure same data type
            img2 = img2.astype(img1.dtype)
            
            # Combine images
            return cv2.bitwise_or(img1, img2)
        
        # Add boundary enhanced versions using the adjusted image
        try:
            boundary_methods = self.enhance_stone_boundaries(adjusted_image)
            preprocessed.update(boundary_methods)
        except Exception as e:
            print(f"Error in boundary enhancement: {e}")
        
        # Create additional combinations
        # 1. CLAHE processing
        try:
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))  # Increased clip limit
            clahe_img = clahe.apply(gray_adjusted)
            preprocessed['clahe'] = clahe_img
            
            # Combine CLAHE with boundary methods
            for method_name, boundary_img in boundary_methods.items():
                combined_key = f'clahe_{method_name}'
                try:
                    preprocessed[combined_key] = safe_combine_images(clahe_img, boundary_img)
                except Exception as e:
                    print(f"Error combining CLAHE with {method_name}: {e}")
        except Exception as e:
            print(f"Error in CLAHE processing: {e}")
        
        # 2. Bilateral filtering with stronger parameters
        try:
            bilateral = cv2.bilateralFilter(gray_adjusted, 11, 100, 100)  # Stronger filtering
            preprocessed['bilateral'] = bilateral
            
            # Combine bilateral with boundary methods
            for method_name, boundary_img in boundary_methods.items():
                combined_key = f'bilateral_{method_name}'
                try:
                    preprocessed[combined_key] = safe_combine_images(bilateral, boundary_img)
                except Exception as e:
                    print(f"Error combining bilateral with {method_name}: {e}")
        except Exception as e:
            print(f"Error in bilateral processing: {e}")
        
        # 3. Add adaptive thresholding
        try:
            adaptive_thresh = cv2.adaptiveThreshold(
                gray_adjusted, 255, 
                cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY_INV, 
                15, 3  # Larger block size and constant
            )
            preprocessed['adaptive_thresh'] = adaptive_thresh
        except Exception as e:
            print(f"Error in adaptive thresholding: {e}")
        
        # Visualization
        if visualize:
            try:
                rows = int(np.ceil(len(preprocessed) / 4))
                cols = min(len(preprocessed), 4)
                
                plt.figure(figsize=(16, 4 * rows))
                for i, (name, img) in enumerate(preprocessed.items()):
                    plt.subplot(rows, cols, i + 1)
                    plt.imshow(img, cmap='gray')
                    plt.title(name)
                    plt.axis('off')
                
                plt.tight_layout()
                plt.show()
            except Exception as e:
                print(f"Visualization error: {e}")
        
        # Store preprocessed images for future use
        self.preprocessed_images = preprocessed
        
        # Also update the display image to use the adjusted version
        self.display_image = adjusted_image
        
        return preprocessed

    def detect_mortar_lines(self, low_threshold=30, high_threshold=200, mortar_brightness=None):
        """
        Detect mortar lines between stones with improved parameters
        
        Parameters:
        - low_threshold: Lower threshold for Canny edge detection
        - high_threshold: Upper threshold for Canny edge detection
        - mortar_brightness: Optional tuple (min, max) for brightness-based mortar detection
        """
        # Use the adjusted image if available, otherwise convert original to grayscale
        if hasattr(self, 'preprocessed_images') and 'adjusted' in self.preprocessed_images:
            gray = self.preprocessed_images['adjusted']
        else:
            gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE with stronger parameters
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Use bilateral filter with stronger parameters
        bilateral = cv2.bilateralFilter(enhanced, 11, 100, 100)
        
        # Create a mask for potential mortar lines based on brightness if specified
        mortar_mask = None
        if mortar_brightness is not None:
            min_val, max_val = mortar_brightness
            # Assume mortar is typically lighter or darker than stones
            mortar_mask = cv2.inRange(bilateral, min_val, max_val)
            
            # Clean up the mask with stronger morphological operations
            kernel = np.ones((3, 3), np.uint8)
            mortar_mask = cv2.morphologyEx(mortar_mask, cv2.MORPH_OPEN, kernel, iterations=2)
            mortar_mask = cv2.morphologyEx(mortar_mask, cv2.MORPH_CLOSE, kernel, iterations=3)
        
        # Use Canny edge detection with adjusted thresholds
        edges = cv2.Canny(bilateral, low_threshold, high_threshold)
        
        # Dilate edges more aggressively
        kernel = np.ones((3, 3), np.uint8)  # Larger kernel
        dilated_edges = cv2.dilate(edges, kernel, iterations=2)  # More iterations
        
        # If we have a mortar mask, combine it with edge detection
        if mortar_mask is not None:
            combined = cv2.bitwise_or(dilated_edges, mortar_mask)
            return combined
        
        return dilated_edges

    def find_stone_contours(self, mortar_lines=None, adaptive_threshold=True):
        """
        Find contours of stones using mortar lines to separate them
        
        Parameters:
        - mortar_lines: Pre-computed mortar lines image (if None, will be computed)
        - adaptive_threshold: Whether to use adaptive thresholding for extra segmentation
        """
        # Get mortar lines if not provided
        if mortar_lines is None:
            mortar_lines = self.detect_mortar_lines()
        
        # Invert the image so stones are white and mortar lines are black
        inverted = 255 - mortar_lines
        
        # Apply stronger morphological operations to connect stone regions
        kernel = np.ones((7, 7), np.uint8)  # Larger kernel
        closed = cv2.morphologyEx(inverted, cv2.MORPH_CLOSE, kernel, iterations=3)  # More iterations
        
        # Fill holes in stone regions
        filled_mask = np.zeros_like(closed)
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter and fill contours with more lenient criteria
        filtered_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 50:  # Lower threshold to catch more potential stones
                continue
            cv2.drawContours(filled_mask, [contour], 0, 255, -1)
            filtered_contours.append(contour)
        
        return filled_mask, filtered_contours

    def watershed_segmentation(self):
        """Use watershed algorithm to segment stones that are touching"""
        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE to enhance contrast
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Apply threshold with more aggressive parameters
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
        
        # Remove noise with larger kernel
        kernel = np.ones((5, 5), np.uint8)
        opening = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=2)
        
        # Sure background area - more dilation
        sure_bg = cv2.dilate(opening, kernel, iterations=4)
        
        # Finding sure foreground area - more aggressive threshold
        dist_transform = cv2.distanceTransform(opening, cv2.DIST_L2, 5)
        _, sure_fg = cv2.threshold(dist_transform, 0.4*dist_transform.max(), 255, 0)
        sure_fg = np.uint8(sure_fg)
        
        # Finding unknown region
        unknown = cv2.subtract(sure_bg, sure_fg)
        
        # Marker labelling
        _, markers = cv2.connectedComponents(sure_fg)
        
        # Add one to all labels so that background is not 0, but 1
        markers = markers + 1
        
        # Mark the unknown region with zero
        markers[unknown == 255] = 0
        
        # Apply watershed
        markers = cv2.watershed(self.original_image, markers)
        
        # Create a mask where boundaries are marked
        boundary_mask = np.zeros_like(gray)
        boundary_mask[markers == -1] = 255
        
        # Dilate boundaries to ensure separation
        boundary_mask = cv2.dilate(boundary_mask, kernel, iterations=1)
        
        # Invert to get regions
        stone_regions = 255 - boundary_mask
        
        # Find contours in the resulting mask
        contours, _ = cv2.findContours(stone_regions, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        return stone_regions, contours

    def try_multiple_approaches(self, preprocess_method=None, visualize=False):
        """
        Try multiple approaches to find the best stone segmentation
        
        Parameters:
        - preprocess_method: Name of preprocessing method to use (if None, uses original image)
        - visualize: Whether to show visualization of results
        """
        # If preprocessed image is specified, use it
        if preprocess_method and preprocess_method in self.preprocessed_images:
            # Create a temporary BGR image for OpenCV functions that expect color
            temp_img = cv2.cvtColor(self.preprocessed_images[preprocess_method], cv2.COLOR_GRAY2BGR)
            
            # Store original image
            orig_img = self.original_image
            
            # Replace original image with preprocessed version temporarily
            self.original_image = temp_img
        
        # Approach 1: Standard edge-based detection
        mortar_lines1 = self.detect_mortar_lines(low_threshold=30, high_threshold=150)
        mask1, contours1 = self.find_stone_contours(mortar_lines1)
        
        # Approach 2: More aggressive edge detection
        mortar_lines2 = self.detect_mortar_lines(low_threshold=20, high_threshold=200)
        mask2, contours2 = self.find_stone_contours(mortar_lines2)
        
        # Approach 3: Try detecting dark mortar
        mortar_lines3 = self.detect_mortar_lines(low_threshold=30, high_threshold=150, 
                                                mortar_brightness=(0, 100))
        mask3, contours3 = self.find_stone_contours(mortar_lines3)
        
        # Approach 4: Try detecting light mortar
        mortar_lines4 = self.detect_mortar_lines(low_threshold=30, high_threshold=150, 
                                                mortar_brightness=(150, 255))
        mask4, contours4 = self.find_stone_contours(mortar_lines4)
        
        # Approach 5: Watershed segmentation
        mask5, contours5 = self.watershed_segmentation()
        
        # Restore original image if we used a preprocessed version
        if preprocess_method and preprocess_method in self.preprocessed_images:
            self.original_image = orig_img
        
        # Filter contours to ensure quality
        filtered_contours1 = self.filter_contours(contours1)
        filtered_contours2 = self.filter_contours(contours2)
        filtered_contours3 = self.filter_contours(contours3)
        filtered_contours4 = self.filter_contours(contours4)
        filtered_contours5 = self.filter_contours(contours5)
        
        # Choose the approach that produced the most reasonable number of contours
        approaches = [
            (len(filtered_contours1), filtered_contours1, mask1, f"Edge-based detection ({preprocess_method})"),
            (len(filtered_contours2), filtered_contours2, mask2, f"Aggressive edge detection ({preprocess_method})"),
            (len(filtered_contours3), filtered_contours3, mask3, f"Dark mortar detection ({preprocess_method})"),
            (len(filtered_contours4), filtered_contours4, mask4, f"Light mortar detection ({preprocess_method})"),
            (len(filtered_contours5), filtered_contours5, mask5, f"Watershed segmentation ({preprocess_method})")
        ]
        
        # Sort by number of contours, but exclude approaches with too many contours (likely noise)
        valid_approaches = [(count, contours, mask, name) for count, contours, mask, name in approaches 
                          if 1 < count < 1000]  # Adjust limits based on expected number of stones
        
        if not valid_approaches:
            # If all approaches gave bad results, take the one with at least some contours
            valid_approaches = [(count, contours, mask, name) for count, contours, mask, name in approaches 
                              if count > 1]
        
        if not valid_approaches:
            # Last resort - take any approach with at least one contour
            valid_approaches = [(count, contours, mask, name) for count, contours, mask, name in approaches 
                              if count > 0]
        
        if not valid_approaches:
            raise ValueError("Could not detect any stones with any approach")
        
        # Choose approach with the most contours (most stones detected)
        best_approach = max(valid_approaches, key=lambda x: x[0])
        best_count, best_contours, best_mask, best_name = best_approach
        
        print(f"Selected approach: {best_name} with {best_count} stones")
        
        if visualize:
            # Show all approaches
            plt.figure(figsize=(15, 15))
            
            for i, (count, _, mask, name) in enumerate(approaches):
                plt.subplot(2, 3, i+1)
                plt.imshow(mask, cmap='gray')
                plt.title(f"{name}\n{count} stones")
                plt.axis('off')
            
            plt.subplot(2, 3, 6)
            vis_img = self.display_image.copy()
            contour_img = cv2.drawContours(vis_img, best_contours, -1, (0, 255, 0), 2)
            plt.imshow(contour_img)
            plt.title(f"Selected: {best_name}")
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
        
        return best_mask, best_contours

    def filter_contours(self, contours, min_area=200, max_aspect_ratio=8.0, convexity_threshold=0.7):
        """Filter contours based on various criteria to ensure quality"""
        filtered = []
        for contour in contours:
            # Get area of the contour
            area = cv2.contourArea(contour)
            
            # Skip very small contours (likely noise)
            if area < min_area:
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check aspect ratio to avoid extremely elongated shapes
            aspect_ratio = max(w, h) / max(1, min(w, h))
            if aspect_ratio > max_aspect_ratio:
                continue
            
            # Check convexity (ratio of contour area to its convex hull area)
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            if hull_area > 0:
                convexity = area / hull_area
                if convexity < convexity_threshold:
                    continue
            
            filtered.append(contour)
        
        return filtered
        
    def find_best_preprocessing(self, visualize=False):
        """Try all preprocessing methods and find the one that detects the most stones"""
        # First, apply all preprocessing methods
        preprocessed = self.preprocess_image(visualize=visualize)
        
        best_method = None
        best_count = 0
        best_contours = None
        best_mask = None
        best_approach_name = None
        
        results = []
        
        # Try original image first
        print("Testing stone detection on original image...")
        mask, contours = self.try_multiple_approaches(preprocess_method=None, visualize=False)
        count = len(contours)
        
        if count > best_count:
            best_count = count
            best_contours = contours
            best_mask = mask
            best_method = "original"
        
        results.append((count, "original"))
        
        # Try each preprocessing method
        for method_name in preprocessed.keys():
            print(f"Testing stone detection with {method_name} preprocessing...")
            mask, contours = self.try_multiple_approaches(preprocess_method=method_name, visualize=False)
            count = len(contours)
            
            results.append((count, method_name))
            
            if count > best_count:
                best_count = count
                best_contours = contours
                best_mask = mask
                best_method = method_name
        
        # Sort results by stone count (descending)
        results.sort(reverse=True)
        
        print("\nPreprocessing method comparison:")
        print("--------------------------------")
        for count, method in results:
            star = " *" if method == best_method else ""
            print(f"{method:<20}: {count} stones{star}")
        
        print(f"\nBest preprocessing method: {best_method} with {best_count} stones detected")
        
        if visualize and best_method:
            # Show original vs best preprocessed
            plt.figure(figsize=(15, 10))
            
            plt.subplot(2, 2, 1)
            plt.imshow(self.display_image)
            plt.title("Original Image")
            plt.axis('off')
            
            plt.subplot(2, 2, 2)
            if best_method == "original":
                plt.imshow(cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB))
            else:
                plt.imshow(self.preprocessed_images[best_method], cmap='gray')
            plt.title(f"Best Preprocessing: {best_method}")
            plt.axis('off')
            
            # Create visualization image
            vis_img = self.display_image.copy()
            
            # Draw contours on the visualization image
            contour_img = cv2.drawContours(vis_img, contours, -1, (0, 255, 0), 2)
            
            plt.subplot(2, 2, 3)
            plt.imshow(mask, cmap='gray')
            plt.title("Stone Mask")
            plt.axis('off')
            
            plt.subplot(2, 2, 4)
            plt.imshow(contour_img)
            plt.title(f"Detected Stones: {best_count}")
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
        
        return best_method, best_mask, best_contours
    
    def extract_stones(self, min_area=200, max_area=500000, max_aspect_ratio=6.0, visualize=False):

        """
        Extract individual stones with minimal filtering to ensure larger stones are included
        
        Parameters:
        - min_area: Minimum stone area in pixels
        - max_area: Maximum stone area in pixels (greatly increased)
        - max_aspect_ratio: Maximum aspect ratio for stone detection (very lenient)
        - visualize: Whether to visualize the extraction process
        
        Returns:
        - List of extracted stones
        """
        # Reset stones list
        self.stones = []
        
        # Find the best preprocessing method and get stone segmentation
        best_method, filled_mask, contours = self.find_best_preprocessing(visualize=False)
        
        print(f"Best preprocessing method: {best_method} with {len(contours)} stones detected")
        
        # Store original contours for visualization
        original_contours = contours.copy()
        
        # Create a list to store all candidate stones before filtering
        candidate_stones = []
        rejected_stones = {'aspect_ratio': 0, 'size': 0, 'other': 0}
        
        # Process each contour to extract a stone
        for i, contour in enumerate(contours):
            # Get area of the contour
            area = cv2.contourArea(contour)
            
            # Skip very small contours (likely noise)
            if area < min_area:
                rejected_stones['size'] += 1
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Skip if bounding box covers almost the whole image (likely the background, not a stone)
            if w > 0.95 * self.img_width and h > 0.95 * self.img_height:
                continue
            
            # Check aspect ratio - be extremely lenient with larger stones
            aspect_ratio = max(w, h) / max(1, min(w, h))
            # For large stones, allow even higher aspect ratios
            adjusted_max_aspect = max_aspect_ratio * 2.0 if area > 10000 else max_aspect_ratio
            if aspect_ratio > adjusted_max_aspect:
                rejected_stones['aspect_ratio'] += 1
                continue
                        
            # Extract the region of interest (bounding rectangle)
            roi_image = self.original_image[y:y+h, x:x+w]
            
            # Create a full rectangular mask (all ones, i.e., full rectangle)
            roi_mask = np.ones((h, w), dtype=np.uint8) * 255
            
            # Create transparent image for the stone
            stone_img = np.zeros((h, w, 4), dtype=np.uint8)
            
            # Copy stone pixels (no need for masking, since we use the full rectangle)
            if roi_image.ndim == 3:
                stone_img[:, :, 0:3] = roi_image
            else:
                stone_img[:, :, 0:3] = np.stack([roi_image]*3, axis=2)
            
            # Set alpha channel to fully opaque (full rectangle)
            stone_img[:, :, 3] = roi_mask    
            
            # Store stone info
            stone_info = {
                'image': stone_img,
                'width': w,
                'height': h,
                'area': area,
                'mask': roi_mask.copy(),
                'aspect_ratio': aspect_ratio,
                'x': x,
                'y': y
            }
            
            # Add to candidate stones
            candidate_stones.append(stone_info)
        
        print(f"Found {len(candidate_stones)} candidate stones before filtering")
        
        # First, automatically include all large stones
        for stone in candidate_stones:
            if stone['area'] > 20000:  # Automatically include all large stones
                self.stones.append(stone)
        
        print(f"Automatically included {len(self.stones)} large stones (area > 20000)")
        
        # Then apply validation to filter the remaining stones
        large_stone_ids = {id(stone) for stone in self.stones}  # Track already included stones
        
        for stone in candidate_stones:
            # Skip stones that were already included
            if id(stone) in large_stone_ids:
                continue
                
            # Check if stone is valid using very lenient criteria
            if self.is_valid_stone(stone, min_area=min_area, max_area=max_area):
                self.stones.append(stone)
            else:
                rejected_stones['other'] += 1
        
        # Sort stones by area (largest first)
        self.stones.sort(key=lambda s: s['area'], reverse=True)
        
        # Print rejection statistics
        print(f"Rejected stones statistics:")
        for reason, count in rejected_stones.items():
            print(f"  - {reason}: {count}")
        
        print(f"Extracted {len(self.stones)} valid stones after filtering")
        
        # If no stones were found, try with extremely lenient parameters
        if len(self.stones) == 0:
            print("No stones passed filtering. Including all candidate stones...")
            # Include all candidate stones
            self.stones = candidate_stones
            print(f"Included all {len(self.stones)} candidate stones")
        
        # Visualize the extraction process if requested
        if visualize and len(self.stones) > 0:
            self.display_extracted_stones(max_stones=30)
        
        return self.stones

    def display_extracted_stones(self, max_stones=30):
        """
        Display the extracted stones in a grid
        
        Parameters:
        - max_stones: Maximum number of stones to display
        """
        if len(self.stones) == 0:
            print("No stones to display")
            return
        
        # Determine how many stones to show
        stones_to_show = min(max_stones, len(self.stones))
        
        # Calculate grid dimensions
        grid_cols = min(5, stones_to_show)
        grid_rows = (stones_to_show + grid_cols - 1) // grid_cols
        
        # Create figure
        plt.figure(figsize=(15, 3 * grid_rows))
        
        # Display stones
        for i in range(stones_to_show):
            stone = self.stones[i]
            plt.subplot(grid_rows, grid_cols, i + 1)
            
            # Display stone with alpha channel
            plt.imshow(stone['image'])
            plt.title(f"Stone {i+1}\nArea: {stone['area']}\n{stone['width']}x{stone['height']}")
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Also show the original image with stone contours
        plt.figure(figsize=(12, 10))
        plt.imshow(cv2.cvtColor(self.display_image, cv2.COLOR_BGR2RGB))

         # Draw rectangles instead of contours
        for stone in self.stones:
            x, y = stone['x'], stone['y']
            w, h = stone['width'], stone['height']
            plt.gca().add_patch(plt.Rectangle((x, y), w, h, fill=False, edgecolor='g', linewidth=2))
        
        # # Draw contours of valid stones
        # for stone in self.stones:
        #     # Create a temporary mask for this stone
        #     temp_mask = np.zeros((self.img_height, self.img_width), dtype=np.uint8)
        #     x, y = stone['x'], stone['y']
        #     temp_mask[y:y+stone['height'], x:x+stone['width']] = stone['mask']
            
        #     # Find contours of this stone in the full image
        #     contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
        #     # Draw contours
        #     for contour in contours:
        #         # Convert contour to list of points for matplotlib
        #         contour_points = contour.reshape(-1, 2)
        #         plt.plot(contour_points[:, 0], contour_points[:, 1], 'g', linewidth=2)
        
        plt.title(f"Original Image with {len(self.stones)} Valid Stones")
        plt.axis('off')
        plt.tight_layout()
        plt.show()

    def generate_wall(self, width=1000, height=800, bg_color=(200, 200, 200), gap=0, visualize=False):
        """
        Generate seamlessly interlocked stone wall - SAME STONES, NO OVERLAPS, NO GAPS
        """
        if not self.stones:
            self.extract_stones()

        valid_stones = [s for s in self.stones if self.is_valid_stone(s)]
        if len(valid_stones) == 0:
            print("No valid stones were extracted. Try adjusting the parameters.")
            return None

        print(f"Using {len(valid_stones)} valid stones for seamless interlocking wall")

        # Create the wall image with background color
        wall_image = Image.new('RGB', (width, height), bg_color)

        # Create precise placement grid to prevent overlaps and gaps
        placement_grid = np.zeros((height, width), dtype=bool)

        # Create stone pool with IDs to avoid array comparison issues
        stone_pool = []
        for i in range(15):  # Create 15 copies of each stone
            for j, stone in enumerate(valid_stones):
                stone_copy = stone.copy()
                stone_copy['id'] = f"{j}_{i}"  # Unique ID for safe removal
                stone_pool.append(stone_copy)

        random.shuffle(stone_pool)
        stones_placed = 0

        # COMPLETELY NEW APPROACH: Pixel-perfect seamless interlocking
        print("Starting PIXEL-PERFECT seamless interlocking...")

        # Start from bottom-left and place stones with absolute zero gaps
        current_x = 0
        current_y = height - 1

        # Place first stone at bottom-left corner
        if stone_pool:
            first_stone = stone_pool.pop(0)
            first_h, first_w = first_stone['image'].shape[:2]
            first_y = height - first_h

            # Place first stone
            stone_pil = Image.fromarray(first_stone['image'])
            wall_image.paste(stone_pil, (0, first_y), stone_pil.split()[3])
            placement_grid[first_y:first_y+first_h, 0:first_w] = True

            stones_placed = 1
            current_x = first_w  # Next stone starts exactly where this one ends
            print(f"Placed foundation stone at (0, {first_y})")

        # Continue placing stones with ZERO gaps
        while stone_pool and stones_placed < 500:  # Limit to prevent infinite loops

            # Find next available position with pixel-perfect placement
            next_pos = self.find_pixel_perfect_position(current_x, current_y, width, height, placement_grid)

            if next_pos is None:
                # Move to next row if current row is full
                current_y = self.find_next_row_position(placement_grid, width, height)
                if current_y < 50:  # Too close to top
                    break
                current_x = 0
                continue

            pos_x, pos_y = next_pos

            # Find stone that fits EXACTLY at this position
            stone_idx = self.find_exact_fitting_stone(stone_pool, pos_x, pos_y, width, height, placement_grid)

            if stone_idx is None:
                # Try next position
                current_x += 1
                continue

            stone = stone_pool.pop(stone_idx)

            # Place stone with ZERO gap
            success = self.place_pixel_perfect_stone(stone, pos_x, pos_y, wall_image, placement_grid)

            if success:
                stones_placed += 1
                # Update current position to right edge of placed stone
                current_x = pos_x + stone['image'].shape[1]

                if stones_placed % 20 == 0:
                    print(f"Seamlessly placed {stones_placed} stones with ZERO gaps")
            else:
                current_x += 1

        # Fill any tiny remaining gaps with small stones
        gaps_filled = self.fill_seamless_gaps(valid_stones, wall_image, placement_grid, width, height)

        print(f"Seamless wall complete: {stones_placed} stones + {gaps_filled} gap fillers")
        print("✓ SAME stone appearance ✓ NO overlaps ✓ NO gaps ✓ Seamless interlocking")

        # Convert to numpy array for return
        wall_array = np.array(wall_image)

        if visualize:
            plt.figure(figsize=(12, 8))
            plt.imshow(wall_array)
            plt.title("Seamlessly Interlocked Stone Wall - No Overlaps, No Gaps")
            plt.axis('off')
            plt.show()

        return wall_array

    def find_pixel_perfect_position(self, start_x, start_y, width, height, placement_grid):
        """Find next position for pixel-perfect placement"""
        try:
            # Look for the first empty position starting from start_x
            for x in range(start_x, width - 20):
                # Find the highest occupied pixel in this column
                for y in range(height):
                    if placement_grid[y, x]:
                        # Found occupied space, return position directly above it
                        return (x, y)

            # If no occupied space found, return bottom position
            return (start_x, start_y)

        except Exception:
            return None

    def find_next_row_position(self, placement_grid, width, height):
        """Find the next row position for placement"""
        try:
            # Find the topmost occupied row
            for y in range(height):
                for x in range(width):
                    if placement_grid[y, x]:
                        return max(50, y - 50)  # Place 50 pixels above

            return height - 100  # Default position

        except Exception:
            return height - 100

    def find_exact_fitting_stone(self, stone_pool, x, y, width, height, placement_grid):
        """Find stone that fits exactly at the given position"""
        for i, stone in enumerate(stone_pool):
            try:
                stone_h, stone_w = stone['image'].shape[:2]
                stone_y = y - stone_h

                # Check bounds
                if (stone_y >= 0 and x + stone_w <= width and
                    stone_y + stone_h <= height):

                    # Check if area is completely free
                    if self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid):
                        return i

            except Exception:
                continue

        return None

    def place_pixel_perfect_stone(self, stone, x, y, wall_image, placement_grid):
        """Place stone with pixel-perfect positioning"""
        try:
            stone_img = stone['image'].copy()
            stone_h, stone_w = stone_img.shape[:2]
            stone_y = y - stone_h

            # Ensure valid placement
            if (stone_y < 0 or x + stone_w > wall_image.width or
                stone_y + stone_h > wall_image.height):
                return False

            # Final check for overlaps
            if not self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid):
                return False

            # Add subtle border for stone definition
            self.add_pixel_perfect_border(stone_img)

            # Place stone with pixel precision
            stone_pil = Image.fromarray(stone_img)
            wall_image.paste(stone_pil, (x, stone_y), stone_pil.split()[3])

            # Mark every pixel as occupied for zero gaps
            placement_grid[stone_y:stone_y+stone_h, x:x+stone_w] = True

            return True

        except Exception:
            return False

    def add_pixel_perfect_border(self, stone_img):
        """Add minimal border for stone definition without creating gaps"""
        h, w = stone_img.shape[:2]

        # Add very thin border only on outer edges
        for py in range(h):
            for px in range(w):
                if stone_img[py, px, 3] > 0:  # If pixel is not transparent
                    # Check if this is an edge pixel
                    is_edge = (py == 0 or py == h-1 or px == 0 or px == w-1 or
                              stone_img[max(0, py-1), px, 3] == 0 or
                              stone_img[min(h-1, py+1), px, 3] == 0 or
                              stone_img[py, max(0, px-1), 3] == 0 or
                              stone_img[py, min(w-1, px+1), 3] == 0)

                    if is_edge:
                        # Very subtle darkening for definition
                        stone_img[py, px, 0:3] = np.maximum(stone_img[py, px, 0:3] - 15, 0)

    def find_interlocking_stone(self, stone_pool, x, y, canvas_width, canvas_height, placement_grid, is_foundation=False):
        """Find stone that interlocks perfectly with zero gaps"""
        suitable_indices = []

        for i, stone in enumerate(stone_pool):
            try:
                stone_w = stone['image'].shape[1]
                stone_h = stone['image'].shape[0]

                if is_foundation:
                    # For foundation, place at bottom edge
                    stone_y = y - stone_h
                    test_x = x
                else:
                    # For upper layers, find exact position that touches stones below
                    stone_y, test_x = self.find_exact_interlocking_position(
                        x, y, stone_w, stone_h, placement_grid
                    )

                # Check bounds
                if (test_x >= 0 and stone_y >= 0 and
                    test_x + stone_w <= canvas_width and stone_y + stone_h <= canvas_height):

                    # Check if area is completely free
                    if self.is_seamless_area_free(test_x, stone_y, stone_w, stone_h, placement_grid):
                        # For non-foundation stones, ensure they touch stones below
                        if is_foundation or self.has_support_below(test_x, stone_y, stone_w, stone_h, placement_grid):
                            suitable_indices.append((i, test_x, stone_y))

            except Exception:
                continue

        if not suitable_indices:
            return None

        # Prefer larger stones for better interlocking
        suitable_indices.sort(
            key=lambda x: stone_pool[x[0]]['image'].shape[0] * stone_pool[x[0]]['image'].shape[1],
            reverse=True
        )

        # Store the exact position for this stone
        choice = suitable_indices[0]
        stone_pool[choice[0]]['exact_x'] = choice[1]
        stone_pool[choice[0]]['exact_y'] = choice[2]

        return choice[0]

    def find_exact_interlocking_position(self, target_x, target_y, stone_w, stone_h, placement_grid):
        """Find exact position where stone sits directly on top of existing stones"""
        # Look for the highest occupied position below target_y
        for test_x in range(max(0, target_x - 10), min(placement_grid.shape[1] - stone_w, target_x + 11)):
            # Find the topmost occupied pixel in this column
            for check_y in range(target_y, placement_grid.shape[0]):
                if placement_grid[check_y, test_x]:
                    # Found occupied space, place stone directly above it
                    stone_y = check_y - stone_h
                    if stone_y >= 0:
                        return stone_y, test_x

        # If no support found, use target position
        return target_y - stone_h, target_x

    def has_support_below(self, x, y, w, h, placement_grid):
        """Check if stone has support directly below it (no floating stones)"""
        try:
            # Check bottom edge of stone for support
            bottom_y = y + h
            if bottom_y >= placement_grid.shape[0]:
                return True  # At bottom edge

            # Check if at least 30% of bottom edge has support
            support_count = 0
            total_width = min(w, placement_grid.shape[1] - x)

            for px in range(x, x + total_width):
                if placement_grid[bottom_y, px]:
                    support_count += 1

            return support_count >= (total_width * 0.3)  # At least 30% support

        except Exception:
            return False

    def find_layer_height(self, placement_grid, layer_num):
        """Find the height for the current layer"""
        try:
            # Find the average height of occupied areas
            occupied_heights = []

            for x in range(0, placement_grid.shape[1], 10):
                for y in range(placement_grid.shape[0]):
                    if placement_grid[y, x]:
                        occupied_heights.append(y)
                        break  # Found topmost occupied pixel in this column

            if not occupied_heights:
                return placement_grid.shape[0] - 50  # Default to near bottom

            # Return slightly above the average occupied height
            avg_height = sum(occupied_heights) / len(occupied_heights)
            return max(50, int(avg_height - 30 - (layer_num * 20)))

        except Exception:
            return placement_grid.shape[0] - 100

    def place_interlocking_stone(self, stone, target_x, target_y, wall_image, placement_grid):
        """Place stone with perfect interlocking (zero gaps)"""
        try:
            # Use exact calculated position
            if 'exact_x' in stone and 'exact_y' in stone:
                x = stone['exact_x']
                stone_y = stone['exact_y']
            else:
                x = target_x
                stone_y = target_y - stone['image'].shape[0]

            stone_img = stone['image'].copy()
            stone_h, stone_w = stone_img.shape[:2]

            # Ensure valid placement
            if (stone_y < 0 or x + stone_w > wall_image.width or
                stone_y + stone_h > wall_image.height):
                return -1, -1

            # Final overlap check
            if not self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid):
                return -1, -1

            # Add border for definition
            self.add_natural_border(stone_img)

            # Place stone
            stone_pil = Image.fromarray(stone_img)
            wall_image.paste(stone_pil, (x, stone_y), stone_pil.split()[3])

            # Mark area as completely occupied (no gaps)
            placement_grid[stone_y:stone_y+stone_h, x:x+stone_w] = True

            return x, stone_y

        except Exception:
            return -1, -1

    def find_natural_placement_spot(self, start_x, start_y, width, height, placement_grid):
        """Find a natural placement spot near the given coordinates"""
        # Search in expanding circles for available space
        for radius in range(0, 100, 10):
            for _ in range(20):  # Try 20 random positions in this radius
                # Random offset within radius
                angle = random.uniform(0, 2 * 3.14159)
                offset_x = int(radius * np.cos(angle))
                offset_y = int(radius * np.sin(angle))

                test_x = max(0, min(width - 50, start_x + offset_x))
                test_y = max(50, min(height - 1, start_y + offset_y))

                # Check if this area has some free space
                try:
                    if not placement_grid[test_y, test_x]:
                        return test_x, test_y
                except:
                    continue

        return -1, -1  # No spot found

    def find_natural_stone_idx(self, stone_pool, x, y, canvas_width, canvas_height, placement_grid):
        """Find stone index that fits seamlessly with adjacent stones"""
        suitable_indices = []

        for i, stone in enumerate(stone_pool):
            try:
                # Try different orientations for better fit
                orientations = [0, 90, 180, 270]
                random.shuffle(orientations)

                for rotation in orientations:
                    # Get rotated stone dimensions
                    if rotation in [90, 270]:
                        stone_w = stone['image'].shape[0]  # height becomes width
                        stone_h = stone['image'].shape[1]  # width becomes height
                    else:
                        stone_w = stone['image'].shape[1]
                        stone_h = stone['image'].shape[0]

                    # Find best fitting position that touches existing stones
                    best_pos = self.find_seamless_contact_position(
                        x, y, stone_w, stone_h, canvas_width, canvas_height, placement_grid
                    )

                    if best_pos is not None:
                        test_x, test_y = best_pos
                        if self.is_seamless_area_free(test_x, test_y, stone_w, stone_h, placement_grid):
                            suitable_indices.append((i, test_x, test_y, rotation))
                            break  # Found good orientation, no need to try others

            except Exception:
                continue

        if not suitable_indices:
            return None

        # Prefer stones that create better contact with existing stones
        suitable_indices.sort(key=lambda x: self.calculate_contact_score(
            x[1], x[2], stone_pool[x[0]], x[3], placement_grid
        ), reverse=True)

        # Add some randomness while preferring better fits
        if len(suitable_indices) > 1:
            top_choices = suitable_indices[:min(3, len(suitable_indices))]
            choice = random.choice(top_choices)
        else:
            choice = suitable_indices[0]

        # Store the best position and orientation for this stone
        stone_pool[choice[0]]['natural_x'] = choice[1]
        stone_pool[choice[0]]['natural_y'] = choice[2]
        stone_pool[choice[0]]['natural_rotation'] = choice[3]

        return choice[0]

    def find_seamless_contact_position(self, target_x, target_y, stone_w, stone_h, canvas_width, canvas_height, placement_grid):
        """Find position where stone touches existing stones seamlessly"""
        # Search around target position for spots that touch existing stones
        search_positions = []

        # Generate positions in expanding circles
        for radius in range(0, 50, 5):
            for angle_deg in range(0, 360, 30):
                angle = angle_deg * 3.14159 / 180
                test_x = int(target_x + radius * np.cos(angle))
                test_y = int(target_y + radius * np.sin(angle))

                # Ensure within bounds
                test_x = max(0, min(canvas_width - stone_w, test_x))
                test_y = max(0, min(canvas_height - stone_h, test_y))

                search_positions.append((test_x, test_y))

        # Find positions that have good contact with existing stones
        best_positions = []
        for test_x, test_y in search_positions:
            contact_score = self.calculate_edge_contact(test_x, test_y, stone_w, stone_h, placement_grid)
            if contact_score > 0:  # Has some contact
                best_positions.append((test_x, test_y, contact_score))

        if not best_positions:
            # If no contact found, return a valid position anyway
            for test_x, test_y in search_positions:
                if (test_x >= 0 and test_y >= 0 and
                    test_x + stone_w <= canvas_width and test_y + stone_h <= canvas_height):
                    return (test_x, test_y)
            return None

        # Sort by contact score and return best
        best_positions.sort(key=lambda x: x[2], reverse=True)
        return (best_positions[0][0], best_positions[0][1])

    def calculate_edge_contact(self, x, y, w, h, placement_grid):
        """Calculate how much contact this position would have with existing stones"""
        contact_score = 0

        try:
            # Check all four edges for contact with existing stones
            # Top edge
            if y > 0:
                for px in range(x, min(x + w, placement_grid.shape[1])):
                    if placement_grid[y - 1, px]:
                        contact_score += 1

            # Bottom edge
            if y + h < placement_grid.shape[0]:
                for px in range(x, min(x + w, placement_grid.shape[1])):
                    if placement_grid[y + h, px]:
                        contact_score += 1

            # Left edge
            if x > 0:
                for py in range(y, min(y + h, placement_grid.shape[0])):
                    if placement_grid[py, x - 1]:
                        contact_score += 1

            # Right edge
            if x + w < placement_grid.shape[1]:
                for py in range(y, min(y + h, placement_grid.shape[0])):
                    if placement_grid[py, x + w]:
                        contact_score += 1

        except Exception:
            pass

        return contact_score

    def calculate_contact_score(self, x, y, stone, rotation, placement_grid):
        """Calculate overall contact score for stone placement"""
        if rotation in [90, 270]:
            w = stone['image'].shape[0]
            h = stone['image'].shape[1]
        else:
            w = stone['image'].shape[1]
            h = stone['image'].shape[0]

        return self.calculate_edge_contact(x, y, w, h, placement_grid)

    def place_natural_stone(self, stone, target_x, target_y, wall_image, placement_grid):
        """Place stone with optimal orientation for seamless contact"""
        try:
            # Use the pre-calculated natural position and rotation if available
            if 'natural_x' in stone and 'natural_y' in stone:
                x = stone['natural_x']
                stone_y = stone['natural_y']
                rotation = stone.get('natural_rotation', 0)
            else:
                stone_h = stone['image'].shape[0]
                x = target_x
                stone_y = target_y - stone_h
                rotation = 0

            stone_img = stone['image'].copy()

            # Apply rotation for better fit
            if rotation != 0:
                stone_img = self.rotate_stone_image(stone_img, rotation)

            stone_h, stone_w = stone_img.shape[:2]

            # Adjust position to ensure seamless contact with existing stones
            x, stone_y = self.adjust_for_seamless_contact(x, stone_y, stone_w, stone_h, placement_grid)

            # Ensure valid placement
            if stone_y < 0 or x + stone_w > wall_image.width or stone_y + stone_h > wall_image.height:
                return -1, -1

            # Final check for overlaps
            if not self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid):
                return -1, -1

            # Add natural border styling
            self.add_natural_border(stone_img)

            # Convert to PIL and place
            stone_pil = Image.fromarray(stone_img)
            wall_image.paste(stone_pil, (x, stone_y), stone_pil.split()[3])

            # Mark area as occupied (fill completely to prevent gaps)
            placement_grid[stone_y:stone_y+stone_h, x:x+stone_w] = True

            return x, stone_y

        except Exception as e:
            return -1, -1

    def rotate_stone_image(self, stone_img, rotation):
        """Rotate stone image by specified degrees"""
        if rotation == 0:
            return stone_img

        h, w = stone_img.shape[:2]
        center = (w // 2, h // 2)

        # Create rotation matrix
        if rotation == 90:
            # 90 degree rotation
            rotated = np.rot90(stone_img, k=1, axes=(1, 0))
        elif rotation == 180:
            # 180 degree rotation
            rotated = np.rot90(stone_img, k=2, axes=(1, 0))
        elif rotation == 270:
            # 270 degree rotation
            rotated = np.rot90(stone_img, k=3, axes=(1, 0))
        else:
            return stone_img

        return rotated

    def adjust_for_seamless_contact(self, x, y, w, h, placement_grid):
        """Adjust position to ensure seamless contact with existing stones"""
        try:
            # Try to move stone to touch existing stones without gaps
            best_x, best_y = x, y
            best_contact = 0

            # Try small adjustments around the target position
            for dx in range(-3, 4):
                for dy in range(-3, 4):
                    test_x = max(0, min(placement_grid.shape[1] - w, x + dx))
                    test_y = max(0, min(placement_grid.shape[0] - h, y + dy))

                    if self.is_seamless_area_free(test_x, test_y, w, h, placement_grid):
                        contact = self.calculate_edge_contact(test_x, test_y, w, h, placement_grid)
                        if contact > best_contact:
                            best_contact = contact
                            best_x, best_y = test_x, test_y

            return best_x, best_y

        except Exception:
            return x, y

    def add_natural_border(self, stone_img):
        """Add natural-looking border with slight variations"""
        h, w = stone_img.shape[:2]
        border_thickness = random.choice([1, 2])  # Vary border thickness

        for py in range(h):
            for px in range(w):
                if stone_img[py, px, 3] > 0:  # If pixel is not transparent
                    # Check if this is a border pixel
                    is_border = False
                    for dy, dx in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                        for thickness in range(border_thickness):
                            ny, nx = py + dy * (thickness + 1), px + dx * (thickness + 1)
                            if (ny < 0 or ny >= h or nx < 0 or nx >= w or stone_img[ny, nx, 3] == 0):
                                is_border = True
                                break
                        if is_border:
                            break

                    if is_border:
                        # Vary border color slightly for natural look
                        border_color = random.choice([[0, 0, 0], [10, 10, 10], [5, 5, 5]])
                        stone_img[py, px, 0:3] = border_color

    def find_seamless_stone_idx(self, stone_pool, x, y, canvas_width, canvas_height, placement_grid):
        """Find stone index that fits seamlessly at position without overlaps or gaps"""
        suitable_indices = []

        for i, stone in enumerate(stone_pool):
            try:
                stone_w = stone['image'].shape[1]  # Use actual image width
                stone_h = stone['image'].shape[0]  # Use actual image height

                # Calculate placement position (stone placed above y)
                stone_y = y - stone_h

                # Check bounds
                if x + stone_w > canvas_width or stone_y < 0:
                    continue

                # Check if area is completely free (no overlaps)
                if self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid):
                    suitable_indices.append(i)

            except Exception:
                continue

        if not suitable_indices:
            return None

        # Prefer stones that create better interlocking (larger stones first)
        suitable_indices.sort(
            key=lambda i: stone_pool[i]['image'].shape[0] * stone_pool[i]['image'].shape[1],
            reverse=True
        )

        # Add some randomness while preferring larger stones
        if len(suitable_indices) > 1:
            top_choices = suitable_indices[:min(3, len(suitable_indices))]
            return random.choice(top_choices)

        return suitable_indices[0]

    def is_seamless_area_free(self, x, y, width, height, placement_grid):
        """Check if area is completely free for seamless placement"""
        try:
            if x < 0 or y < 0 or x + width > placement_grid.shape[1] or y + height > placement_grid.shape[0]:
                return False

            # Check if any pixel in the area is already occupied
            return not np.any(placement_grid[y:y+height, x:x+width])

        except Exception:
            return False

    def place_seamless_stone(self, stone, x, y, wall_image, placement_grid):
        """Place stone seamlessly with exact positioning to prevent gaps and overlaps"""
        try:
            stone_img = stone['image'].copy()
            stone_h, stone_w = stone_img.shape[:2]

            # Calculate exact placement position
            stone_y = y - stone_h

            # Ensure valid placement
            if stone_y < 0 or x + stone_w > wall_image.width:
                return -1, -1

            # Double-check no overlap
            if not self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid):
                return -1, -1

            # Add the same border styling as original
            h, w = stone_img.shape[:2]
            for py in range(h):
                for px in range(w):
                    if stone_img[py, px, 3] > 0:  # If pixel is not transparent
                        # Check if this is a border pixel
                        is_border = False
                        for dy, dx in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                            ny, nx = py + dy, px + dx
                            if (ny < 0 or ny >= h or nx < 0 or nx >= w or stone_img[ny, nx, 3] == 0):
                                is_border = True
                                break

                        if is_border:
                            stone_img[py, px, 0:3] = [0, 0, 0]  # Black border

            # Convert to PIL and place
            stone_pil = Image.fromarray(stone_img)
            wall_image.paste(stone_pil, (x, stone_y), stone_pil.split()[3])

            # Mark area as occupied in placement grid (seamless, no gaps)
            placement_grid[stone_y:stone_y+stone_h, x:x+stone_w] = True

            return x, stone_y

        except Exception as e:
            print(f"Error in seamless placement: {e}")
            return -1, -1

    def find_next_seamless_level(self, placement_grid, current_y):
        """Find next level for seamless stone placement"""
        height, width = placement_grid.shape

        # Look for next available level going upward
        for y in range(current_y - 1, -1, -1):
            # Check if this level has any free space
            if not np.all(placement_grid[y, :]):
                return y

        return -1

    def fill_seamless_gaps(self, stone_pool, wall_image, placement_grid, width, height):
        """Fill remaining small gaps seamlessly"""
        gaps_filled = 0
        small_stones = []

        # Create list of small stones with indices for safe removal
        for stone in stone_pool:
            if stone['image'].shape[0] * stone['image'].shape[1] < 1500:
                small_stones.append(stone)

        # Scan for gaps and fill them
        for y in range(height - 1, -1, -5):
            for x in range(0, width, 5):
                try:
                    if not placement_grid[y, x]:  # Found empty spot
                        # Try to fill with small stone
                        stones_to_remove = []
                        for i, stone in enumerate(small_stones):
                            try:
                                stone_h, stone_w = stone['image'].shape[:2]
                                stone_y = y - stone_h

                                if (stone_y >= 0 and
                                    x + stone_w <= width and
                                    self.is_seamless_area_free(x, stone_y, stone_w, stone_h, placement_grid)):

                                    # Place the gap filler
                                    placed_x, placed_y = self.place_seamless_stone(
                                        stone, x, y, wall_image, placement_grid
                                    )

                                    if placed_x >= 0:
                                        gaps_filled += 1
                                        stones_to_remove.append(i)
                                        break

                            except Exception:
                                continue

                        # Remove used stones (in reverse order to maintain indices)
                        for i in reversed(stones_to_remove):
                            small_stones.pop(i)

                except Exception:
                    continue

        return gaps_filled

    # Additional helper method to enhance stone variety
    def enhance_stone_variety(self, stones):
        """
        Enhance stone variety by creating variations of existing stones
        to better match ledgestone characteristics.
        """
        enhanced_stones = stones.copy()
        
        for stone in stones:
            # Create horizontal variations (stretch/compress width slightly)
            for width_factor in [0.8, 1.2, 1.5]:
                if width_factor != 1.0:
                    new_width = int(stone['width'] * width_factor)
                    if new_width > 20 and new_width < 300:  # Reasonable size limits
                        new_img = cv2.resize(stone['image'], (new_width, stone['height']))
                        enhanced_stones.append({
                            'image': new_img,
                            'width': new_width,
                            'height': stone['height'],
                            'area': new_width * stone['height']
                        })
        
        return enhanced_stones

    def save_wall(self, output_path, width=1000, height=800, bg_color=(200, 200, 200), gap=2):
        """Generate and save a Tetris-style stone wall to the specified path"""
        # Generate the wall (returns a numpy array)
        wall_array = self.generate_wall(width, height, bg_color, 0, visualize=False)
        
        if wall_array is None:
            print("Failed to generate wall")
            return None
        
        # Convert numpy array to PIL Image
        wall_image = Image.fromarray(wall_array)
        
        # Save the image
        wall_image.save(output_path)
        print(f"Tetris-style stone wall saved to {output_path}")
        return wall_array

    def extend_partial_stones(self, stone_mask, original_image, max_iterations=5):
        """
        Enhanced method to extend partial stones to their likely full shape
        
        Parameters:
        - stone_mask: Binary mask of the detected stone
        - original_image: Original image to sample texture from
        - max_iterations: Maximum number of dilation iterations
        
        Returns:
        - Extended stone mask
        """
        # Convert to grayscale if needed
        if len(original_image.shape) > 2:
            gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
        else:
            gray_image = original_image.copy()
        
        # Create a copy of the mask
        extended_mask = stone_mask.copy()
        
        # Calculate texture statistics for the current stone
        stone_pixels = gray_image[stone_mask > 0]
        if len(stone_pixels) == 0:
            return stone_mask
        
        mean_intensity = np.mean(stone_pixels)
        std_intensity = np.std(stone_pixels)
        
        # Create a kernel for dilation
        kernel = np.ones((3, 3), np.uint8)
        
        # Adaptive similarity threshold based on texture variance
        # More variance = more lenient threshold
        base_threshold = 2.0
        similarity_threshold = base_threshold * (1 + std_intensity / 50)
        
        # Check if the stone is likely partial (touching the border)
        h, w = stone_mask.shape
        border_pixels = np.sum(stone_mask[0, :]) + np.sum(stone_mask[-1, :]) + \
                    np.sum(stone_mask[:, 0]) + np.sum(stone_mask[:, -1])
        
        is_partial = border_pixels > 0
        
        # If the stone is partial, be more aggressive with extension
        if is_partial:
            max_iterations += 3
            similarity_threshold *= 1.5
        
        # Iteratively extend the stone
        for i in range(max_iterations):
            # Dilate the current mask
            dilated = cv2.dilate(extended_mask, kernel, iterations=1)
            
            # Find new pixels (in dilated but not in current mask)
            new_pixels_mask = cv2.subtract(dilated, extended_mask)
            
            # If no new pixels, stop
            if np.sum(new_pixels_mask) == 0:
                break
            
            # Check texture similarity for new pixels
            new_pixel_coords = np.where(new_pixels_mask > 0)
            new_pixel_values = gray_image[new_pixel_coords]
            
            # Calculate z-scores for new pixels
            z_scores = np.abs(new_pixel_values - mean_intensity) / (std_intensity + 1e-5)
            
            # Create mask of similar pixels
            similar_pixels = z_scores < similarity_threshold
            
            # If no similar pixels, stop
            if np.sum(similar_pixels) == 0:
                break
            
            # Create a mask of the similar new pixels
            similar_mask = np.zeros_like(new_pixels_mask)
            similar_coords = (new_pixel_coords[0][similar_pixels], new_pixel_coords[1][similar_pixels])
            similar_mask[similar_coords] = 255
            
            # Add similar pixels to the extended mask
            extended_mask = cv2.bitwise_or(extended_mask, similar_mask)
            
            # Update texture statistics (rolling average)
            stone_pixels = gray_image[extended_mask > 0]
            if len(stone_pixels) > 0:
                mean_intensity = 0.7 * mean_intensity + 0.3 * np.mean(stone_pixels)
                std_intensity = 0.7 * std_intensity + 0.3 * np.std(stone_pixels)
        
        # Final cleanup with morphological operations
        extended_mask = cv2.morphologyEx(extended_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
        
        return extended_mask

    def is_valid_stone(self, stone, min_area=200, max_area=500000, min_area_ratio=0.3, min_fullness=0.5):
        """
        Extremely lenient validation to filter out only the most obvious partial stones and multi-stone instances
        
        Parameters:
        - stone: Stone dictionary containing image, mask, etc.
        - min_area: Minimum absolute area in pixels
        - max_area: Maximum absolute area in pixels (greatly increased)
        - min_area_ratio: Minimum ratio of stone area to bounding box area (very lenient)
        - min_fullness: Minimum ratio of stone area to convex hull area (very lenient)
        
        Returns:
        - Boolean indicating if the stone is valid
        """
        # Check minimum area only - be extremely lenient with maximum area
        if stone['area'] < min_area:
            return False
        
        # For very large stones, skip most checks and just accept them
        if stone['area'] > 20000:
            # Only check if it's not extremely elongated
            if stone['aspect_ratio'] > 8.0:
                return False
            return True
        
        # For medium and small stones, apply some basic checks
        
        # Get the mask
        mask = stone['mask']
        
        # Check area ratio (stone area / bounding box area) - very lenient
        bbox_area = stone['width'] * stone['height']
        area_ratio = stone['area'] / max(1, bbox_area)
        if area_ratio < min_area_ratio:
            return False
        
        # Check fullness (stone area / convex hull area) - very lenient
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return False
        
        hull = cv2.convexHull(contours[0])
        hull_mask = np.zeros_like(mask)
        cv2.drawContours(hull_mask, [hull], 0, 255, -1)
        
        hull_area = np.sum(hull_mask > 0)
        fullness = stone['area'] / max(1, hull_area)
        if fullness < min_fullness:
            return False
        
        # Check for extremely fragmented regions (likely multiple stones)
        num_labels, _ = cv2.connectedComponents(mask)
        if num_labels > 10:  # Very lenient - allow up to 9 disconnected regions
            return False
        
        # Check circularity only for small stones
        if stone['area'] < 5000:
            perimeter = cv2.arcLength(contours[0], True)
            circularity = 4 * np.pi * stone['area'] / (perimeter * perimeter + 1e-6)
            if circularity < 0.15:  # Extremely lenient
                return False
        
        return True

    def separate_connected_stones(self, mask):
        """
        Advanced method to separate connected stones using multiple techniques
        
        Parameters:
        - mask: Binary mask containing potentially connected stones
        
        Returns:
        - List of masks, each containing a single stone
        """
        # Make a copy of the mask
        mask_copy = mask.copy()
        
        # Step 1: Apply distance transform to find stone centers
        dist_transform = cv2.distanceTransform(mask_copy, cv2.DIST_L2, 5)
        
        # Step 2: Find local maxima in the distance transform (stone centers)
        # Normalize distance transform
        cv2.normalize(dist_transform, dist_transform, 0, 1.0, cv2.NORM_MINMAX)
        
        # Apply multiple thresholds to find peaks at different levels
        peaks_masks = []
        for thresh in [0.7, 0.6, 0.5, 0.4]:
            _, peaks = cv2.threshold(dist_transform, thresh, 1.0, cv2.THRESH_BINARY)
            peaks = np.uint8(peaks * 255)
            # Clean up noise in local maxima
            kernel = np.ones((3, 3), np.uint8)
            peaks = cv2.morphologyEx(peaks, cv2.MORPH_OPEN, kernel, iterations=1)
            peaks_masks.append(peaks)
        
        # Combine peaks from different thresholds
        combined_peaks = np.zeros_like(peaks_masks[0])
        for peaks in peaks_masks:
            combined_peaks = cv2.bitwise_or(combined_peaks, peaks)
        
        # Find connected components in combined peaks
        _, markers = cv2.connectedComponents(combined_peaks)
        
        # Add one to all labels so that background is not 0
        markers = markers + 1
        
        # Mark the unknown region (not stone centers) with 0
        markers[mask_copy == 0] = 0
        
        # Step 3: Apply watershed to separate stones
        # Convert mask to BGR for watershed
        mask_bgr = cv2.cvtColor(mask_copy, cv2.COLOR_GRAY2BGR)
        cv2.watershed(mask_bgr, markers)
        
        # Step 4: Extract each stone as a separate mask
        separated_masks = []
        for label in range(2, markers.max() + 1):  # Skip background (0) and unknown (1)
            # Create a mask for this stone
            stone_mask = np.zeros_like(mask_copy)
            stone_mask[markers == label] = 255
            
            # Only add if it has a reasonable size
            if np.sum(stone_mask > 0) > 100:
                # Clean up the mask with morphological operations
                kernel = np.ones((3, 3), np.uint8)
                stone_mask = cv2.morphologyEx(stone_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
                separated_masks.append(stone_mask)
        
        # If watershed didn't separate well, try additional methods
        if len(separated_masks) <= 1 and np.sum(mask_copy > 0) > 1000:
            # Method 2: Try separating using concavity analysis
            contours, _ = cv2.findContours(mask_copy, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                main_contour = max(contours, key=cv2.contourArea)
                
                # Find convex hull
                hull = cv2.convexHull(main_contour, returnPoints=False)
                
                # Find convexity defects
                try:
                    defects = cv2.convexityDefects(main_contour, hull)
                    
                    if defects is not None and len(defects) > 0:
                        # Sort defects by depth
                        defects = sorted(defects, key=lambda x: x[0][3], reverse=True)
                        
                        # Use the deepest defects to create separation lines
                        separation_mask = np.zeros_like(mask_copy)
                        
                        # Draw lines between significant defects and to the center
                        M = cv2.moments(main_contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            
                            for i in range(min(len(defects), 10)):  # Use more defects
                                s, e, f, d = defects[i][0]
                                start = tuple(main_contour[s][0])
                                end = tuple(main_contour[e][0])
                                far = tuple(main_contour[f][0])
                                
                                # Only use deep defects
                                if d / 256.0 > 5:  # Lower threshold to catch more defects
                                    # Draw line from defect point to contour center
                                    cv2.line(separation_mask, far, (cx, cy), 255, 2)
                                    
                                    # Also draw lines between nearby defect points
                                    for j in range(i+1, min(len(defects), 10)):
                                        s2, e2, f2, d2 = defects[j][0]
                                        far2 = tuple(main_contour[f2][0])
                                        
                                        # Calculate distance between defect points
                                        dist = np.sqrt((far[0]-far2[0])**2 + (far[1]-far2[1])**2)
                                        
                                        # If defects are close enough, connect them
                                        if dist < mask_copy.shape[0] * 0.5:  # Threshold based on image size
                                            cv2.line(separation_mask, far, far2, 255, 2)
                    
                    # Apply the separation lines to the original mask
                    separated = cv2.subtract(mask_copy, separation_mask)
                    
                    # Find connected components in the separated mask
                    num_labels, labels = cv2.connectedComponents(separated)
                    
                    # Create a mask for each component
                    for label in range(1, num_labels):
                        component_mask = np.zeros_like(mask_copy)
                        component_mask[labels == label] = 255
                        
                        # Only add if it has a reasonable size
                        if np.sum(component_mask > 0) > 100:
                            separated_masks.append(component_mask)
                except:
                    pass
            
            # Method 3: Try separating using morphological operations
            if len(separated_masks) <= 1:
                # Try erosion to separate touching stones
                kernel = np.ones((5, 5), np.uint8)
                eroded = cv2.erode(mask_copy, kernel, iterations=2)
                
                # Find connected components in eroded mask
                num_labels, labels = cv2.connectedComponents(eroded)
                
                if num_labels > 2:  # If we found multiple components
                    # Dilate each component separately to recover original shape
                    for label in range(1, num_labels):
                        component = np.zeros_like(mask_copy)
                        component[labels == label] = 255
                        
                        # Dilate to recover original shape, but don't let it grow beyond original mask
                        dilated = cv2.dilate(component, kernel, iterations=2)
                        dilated = cv2.bitwise_and(dilated, mask_copy)
                        
                        # Only add if it has a reasonable size
                        if np.sum(dilated > 0) > 100:
                            separated_masks.append(dilated)
        
        # If we still couldn't separate, return the original mask
        if len(separated_masks) == 0:
            separated_masks.append(mask_copy)
        
        return separated_masks

    def adjust_saturation_highlights(self, image, saturation_factor=-80, highlight_factor=80):
            """
            Adjust saturation and highlights of an image
            
            Parameters:
            - image: Input image (BGR format)
            - saturation_factor: Amount to adjust saturation (-100 to 100)
            - highlight_factor: Amount to adjust highlights (0 to 100)
            
            Returns:
            - Adjusted image
            """
            # Convert to HSV for saturation adjustment
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Adjust saturation
            h, s, v = cv2.split(hsv)
            
            # Scale saturation_factor to appropriate range (-100 to 100 -> -255 to 255)
            sat_adjust = int(saturation_factor * 2.55)
            
            # Apply saturation adjustment
            s = np.clip(s.astype(np.int16) + sat_adjust, 0, 255).astype(np.uint8)
            
            # Adjust highlights (increase brightness in brighter areas)
            highlight_threshold = 255 - highlight_factor
            highlight_mask = v > highlight_threshold
            
            # Apply highlight adjustment
            v_adjusted = v.copy()
            v_adjusted[highlight_mask] = np.clip(
                v_adjusted[highlight_mask] + (v_adjusted[highlight_mask] - highlight_threshold) * highlight_factor / 255, 
                0, 
                255
            ).astype(np.uint8)
            
            # Merge channels back
            hsv_adjusted = cv2.merge([h, s, v_adjusted])
            
            # Convert back to BGR
            adjusted = cv2.cvtColor(hsv_adjusted, cv2.COLOR_HSV2BGR)
            
            return adjusted

# Example usage
def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Stone Wall Generator')
    parser.add_argument('input_image', help='Path to the input stone wall image')
    parser.add_argument('output_image', help='Path to save the generated wall')
    parser.add_argument('--width', type=int, default=1200, help='Width of output wall in pixels')
    parser.add_argument('--height', type=int, default=800, help='Height of output wall in pixels')
    parser.add_argument('--gap', type=int, default=2, help='Gap between stones in pixels')
    parser.add_argument('--visualize', action='store_true', help='Visualize the stone detection process')
    parser.add_argument('--min-area', type=int, default=200, help='Minimum area for stone detection')
    parser.add_argument('--max-aspect', type=float, default=8.0, help='Maximum aspect ratio for stone detection')
    parser.add_argument('--bg-color', type=str, default='200,200,200', 
                        help='Background color as R,G,B (e.g., 200,200,200)')
    
    return parser.parse_args()

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    try:
        # Parse background color
        bg_color = tuple(map(int, args.bg_color.split(',')))
        if len(bg_color) != 3:
            raise ValueError("Background color must be in format R,G,B")
    except Exception as e:
        print(f"Error parsing background color: {e}")
        print("Using default background color (200,200,200)")
        bg_color = (200, 200, 200)
    
    try:
        # Create generator
        print(f"Loading image from {args.input_image}")
        generator = StoneWallGenerator(args.input_image)
        
        # Apply preprocessing and extract stones
        print("Applying image preprocessing and detecting stones...")
        print("This may take a moment as multiple methods are being evaluated...")
        
        generator.extract_stones(min_area=args.min_area, 
                                max_aspect_ratio=args.max_aspect,
                                visualize=args.visualize)
        
        if len(generator.stones) == 0:
            print("No stones were detected! Try adjusting the min-area and max-aspect parameters.")
            sys.exit(1)
        
        # Display extracted stones if visualization is enabled
        if args.visualize:
            print("Displaying extracted stones...")
            generator.display_extracted_stones(max_stones=30)  # Show more stones
        
        # Generate and save wall
        print(f"Generating wall ({args.width}x{args.height})...")
        generator.save_wall(args.output_image, 
                           width=args.width, 
                           height=args.height, 
                           bg_color=bg_color,
                           gap=args.gap)
        
        print(f"Stone wall successfully generated and saved to {args.output_image}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
