#!/usr/bin/env python3
"""
Test the updated ashlar stone wall generator using detected stones
"""

import sys
import os

# Import the updated stone wall generator
sys.path.append('.')

# Import from the file with spaces in name
import importlib.util
spec = importlib.util.spec_from_file_location("stackStones4_Copy", "stackStones4 - Copy.py")
stackStones4_Copy = importlib.util.module_from_spec(spec)
spec.loader.exec_module(stackStones4_Copy)
StoneWallGenerator = stackStones4_Copy.StoneWallGenerator

def test_updated_ashlar_wall():
    """Test the updated ashlar stone wall generation with detected stones"""
    
    print("Testing Updated Ashlar Stone Wall Generator")
    print("=" * 50)
    
    # Test with available stone images
    stone_images = ['stones1.jpg', 'stones2.jpg']
    
    for stone_image in stone_images:
        if os.path.exists(stone_image):
            print(f"\nTesting with {stone_image}...")
            
            try:
                # Create stone wall generator
                generator = StoneWallGenerator(stone_image)
                
                # Extract stones from the image
                print("Extracting stones from image...")
                stones = generator.extract_stones(visualize=False)
                
                if stones:
                    print(f"✓ Extracted {len(stones)} stones")
                    
                    # Generate ashlar wall using detected stones
                    print("Generating ashlar stone wall...")
                    output_path = f"ashlar_wall_from_{stone_image.replace('.jpg', '.png')}"
                    
                    wall_array = generator.save_wall(
                        output_path=output_path,
                        width=1200,
                        height=800,
                        bg_color=(220, 215, 210),
                        gap=2
                    )
                    
                    if wall_array is not None:
                        print(f"✓ Successfully generated ashlar wall from {stone_image}")
                        print(f"✓ Saved to: {output_path}")
                        
                        # Display the wall if matplotlib is available
                        try:
                            import matplotlib.pyplot as plt
                            plt.figure(figsize=(15, 10))
                            plt.imshow(wall_array)
                            plt.title(f"Dry-Stack Ashlar Wall from {stone_image}", fontsize=16)
                            plt.axis('off')
                            plt.tight_layout()
                            plt.show()
                            print("✓ Wall displayed successfully")
                        except ImportError:
                            print("! Matplotlib not available for display")
                    else:
                        print(f"✗ Failed to generate wall from {stone_image}")
                else:
                    print(f"✗ No stones extracted from {stone_image}")
                    
            except Exception as e:
                print(f"✗ Error processing {stone_image}: {e}")
        else:
            print(f"! Stone image {stone_image} not found")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_updated_ashlar_wall()
