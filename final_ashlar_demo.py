#!/usr/bin/env python3
"""
Final demonstration of the fixed ashlar stone wall generation
"""

import numpy as np
import random
from PIL import Image
import matplotlib.pyplot as plt

def create_demo_stones():
    """Create demo stones to simulate detected stones"""
    stones = []
    
    # Create various stone sizes and shapes
    stone_configs = [
        # Large stones
        (120, 60, (180, 170, 160)),
        (150, 70, (160, 150, 140)),
        (100, 80, (170, 160, 150)),
        
        # Medium stones  
        (80, 50, (175, 165, 155)),
        (90, 45, (165, 155, 145)),
        (70, 60, (155, 145, 135)),
        
        # Small stones
        (50, 40, (185, 175, 165)),
        (60, 35, (150, 140, 130)),
        (45, 45, (160, 150, 140)),
        
        # Linear stones
        (180, 40, (170, 160, 150)),
        (160, 35, (175, 165, 155)),
        (140, 45, (165, 155, 145)),
    ]
    
    # Create multiple variations of each stone type
    for width, height, base_color in stone_configs:
        for _ in range(random.randint(3, 8)):  # Create 3-8 of each type
            # Add some variation to dimensions
            w = width + random.randint(-10, 10)
            h = height + random.randint(-5, 5)
            
            # Ensure minimum size
            w = max(30, w)
            h = max(25, h)
            
            # Create stone image
            stone_img = create_stone_image(w, h, base_color)
            
            stone_info = {
                'image': stone_img,
                'width': w,
                'height': h,
                'area': w * h
            }
            stones.append(stone_info)
    
    return stones

def create_stone_image(width, height, base_color):
    """Create a realistic stone image"""
    stone_img = np.zeros((height, width, 4), dtype=np.uint8)
    
    # Add base color with variation
    for y in range(height):
        for x in range(width):
            color_var = random.randint(-20, 20)
            r = max(0, min(255, base_color[0] + color_var))
            g = max(0, min(255, base_color[1] + color_var))
            b = max(0, min(255, base_color[2] + color_var))
            stone_img[y, x] = [r, g, b, 255]
    
    # Add texture lines
    for _ in range(random.randint(2, 6)):
        if random.random() < 0.7:  # Horizontal lines
            y_line = random.randint(1, height-2)
            for x in range(width):
                if random.random() < 0.6:
                    stone_img[y_line, x, 0:3] = [max(0, c - 15) for c in stone_img[y_line, x, 0:3]]
        else:  # Vertical lines
            x_line = random.randint(1, width-2)
            for y in range(height):
                if random.random() < 0.6:
                    stone_img[y, x_line, 0:3] = [max(0, c - 15) for c in stone_img[y, x_line, 0:3]]
    
    # Add clean borders
    border_color = [max(0, c - 25) for c in base_color]
    for x in range(width):
        stone_img[0, x, 0:3] = border_color
        stone_img[height-1, x, 0:3] = border_color
    for y in range(height):
        stone_img[y, 0, 0:3] = border_color
        stone_img[y, width-1, 0:3] = border_color
    
    return stone_img

def generate_ashlar_wall(stones, width=1200, height=800, bg_color=(220, 215, 210)):
    """Generate ashlar stone wall with proper non-overlapping placement"""
    
    print(f"Generating ashlar wall with {len(stones)} stones...")
    
    # Create wall image
    wall_image = Image.new('RGB', (width, height), bg_color)
    
    # Track placed stones
    placed_stones = []
    stones_placed = 0
    
    # Sort stones by area (largest first) for better stability
    try:
        stones.sort(key=lambda s: s.get('area', 0), reverse=True)
    except:
        pass  # Use original order if sorting fails
    
    # Build from bottom up in courses
    current_y = height
    course_num = 0
    
    while current_y > 50 and stones_placed < len(stones):
        course_num += 1
        print(f"Building course {course_num}")
        
        # Select available stones (not yet used)
        used_stone_ids = set(id(ps.get('stone_ref')) for ps in placed_stones if ps.get('stone_ref'))
        available_stones = [s for s in stones if id(s) not in used_stone_ids]
        
        if not available_stones:
            break
        
        # Determine course height
        if course_num <= 2:
            # Bottom courses: prefer larger stones
            course_stones_pool = [s for s in available_stones if s['area'] > np.median([st['area'] for st in available_stones])]
            if not course_stones_pool:
                course_stones_pool = available_stones
        else:
            course_stones_pool = available_stones
        
        course_height = min(80, max(s['height'] for s in course_stones_pool[:10]))
        course_y = current_y - course_height
        
        # Add joint staggering offset
        if course_num > 1:
            # Find previous course joints to avoid alignment
            prev_joints = []
            for stone in placed_stones:
                if stone['y'] >= course_y + course_height - 20:
                    prev_joints.append(stone['x'] + stone['width'])
            
            # Start with offset to stagger joints
            start_offset = random.randint(10, 50)
            # Adjust if it aligns with previous joints
            for joint_x in prev_joints:
                if abs(start_offset - (joint_x % width)) < 20:
                    start_offset = (start_offset + 30) % 80
        else:
            start_offset = 0
        
        # Fill course from left to right
        current_x = start_offset
        course_stones_placed = 0
        
        while current_x < width - 50 and course_stones_placed < 20:
            # Find stones that fit
            remaining_width = width - current_x
            suitable_stones = []
            
            for stone in course_stones_pool:
                try:
                    stone_h, stone_w = stone['image'].shape[:2]
                    if stone_w <= remaining_width and stone_h <= course_height + 20:
                        suitable_stones.append(stone)
                except:
                    continue
            
            if not suitable_stones:
                break
            
            # Select stone (prefer variety)
            if len(suitable_stones) > 1 and course_stones_placed > 0:
                try:
                    last_stone = placed_stones[-1]
                    different_stones = []
                    for s in suitable_stones:
                        try:
                            if (abs(s.get('width', 0) - last_stone.get('width', 0)) > 15 or 
                                abs(s.get('height', 0) - last_stone.get('height', 0)) > 10):
                                different_stones.append(s)
                        except:
                            different_stones.append(s)
                    if different_stones:
                        suitable_stones = different_stones
                except:
                    pass
            
            stone = random.choice(suitable_stones)
            
            try:
                # Get stone dimensions
                stone_img = stone['image']
                stone_h, stone_w = stone_img.shape[:2]
                
                # Calculate position
                stone_x = current_x
                stone_y = course_y + (course_height - stone_h) // 2
                
                # Ensure stone doesn't go below bottom
                if stone_y + stone_h > height:
                    stone_y = height - stone_h
                
                # Check for overlaps
                overlap = False
                for placed_stone in placed_stones:
                    if (stone_x < placed_stone['x'] + placed_stone['width'] and
                        stone_x + stone_w > placed_stone['x'] and
                        stone_y < placed_stone['y'] + placed_stone['height'] and
                        stone_y + stone_h > placed_stone['y']):
                        overlap = True
                        break
                
                # Check structural support for upper courses
                has_support = True
                if course_num > 1:
                    support_area = 0
                    stone_bottom = stone_y + stone_h
                    
                    for placed_stone in placed_stones:
                        if (placed_stone['y'] <= stone_bottom <= placed_stone['y'] + placed_stone['height'] + 10):
                            overlap_start = max(stone_x, placed_stone['x'])
                            overlap_end = min(stone_x + stone_w, placed_stone['x'] + placed_stone['width'])
                            if overlap_end > overlap_start:
                                support_area += overlap_end - overlap_start
                    
                    # Require at least 40% support
                    required_support = stone_w * 0.4
                    has_support = support_area >= required_support
                
                if not overlap and has_support:
                    # Place stone
                    if stone_img.ndim == 4:
                        stone_pil = Image.fromarray(stone_img)
                        wall_image.paste(stone_pil, (stone_x, stone_y), stone_pil)
                    else:
                        stone_pil = Image.fromarray(stone_img).convert('RGB')
                        wall_image.paste(stone_pil, (stone_x, stone_y))
                    
                    # Record placement
                    placed_stones.append({
                        'x': stone_x,
                        'y': stone_y,
                        'width': stone_w,
                        'height': stone_h,
                        'stone_ref': stone
                    })
                    stones_placed += 1
                    course_stones_placed += 1
                    
                    # Remove stone from pool
                    try:
                        course_stones_pool.remove(stone)
                    except ValueError:
                        pass
                
                # Move to next position
                current_x += stone_w + 2
                
            except Exception as e:
                print(f"Error placing stone: {e}")
                try:
                    if stone in course_stones_pool:
                        course_stones_pool.remove(stone)
                except ValueError:
                    pass
                continue
        
        # Move to next course
        current_y = course_y
    
    print(f"Placed {stones_placed} stones in {course_num} courses")
    return np.array(wall_image)

def main():
    """Main demonstration"""
    print("Final Ashlar Stone Wall Demonstration")
    print("=" * 50)
    
    # Create demo stones
    print("Creating demo stones...")
    stones = create_demo_stones()
    print(f"Created {len(stones)} demo stones")
    
    # Generate ashlar wall
    print("Generating ashlar stone wall...")
    wall_array = generate_ashlar_wall(stones, 1400, 900)
    
    # Save wall
    wall_image = Image.fromarray(wall_array)
    output_path = "final_ashlar_demo.png"
    wall_image.save(output_path)
    print(f"Wall saved to {output_path}")
    
    # Display wall
    plt.figure(figsize=(16, 12))
    plt.imshow(wall_array)
    plt.title("Final Dry-Stack Ashlar Stone Wall\n(No Overlapping, Proper Masonry Construction)", fontsize=18)
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    print("\n✓ Successfully generated ashlar stone wall!")
    print("✓ Features:")
    print("  - No overlapping stones")
    print("  - Proper joint staggering")
    print("  - Structural support requirements")
    print("  - Larger stones at base")
    print("  - Natural irregular pattern")
    print("  - Clean ashlar appearance")

if __name__ == "__main__":
    main()
