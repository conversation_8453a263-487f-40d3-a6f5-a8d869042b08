import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage import measure, filters, morphology, exposure
from skimage.segmentation import watershed, mark_boundaries
from skimage.feature import peak_local_max, canny
from scipy import ndimage
import random
from PIL import Image, ImageDraw, ImageFont
import os
import argparse
import sys
from itertools import product

class StoneWallGenerator:
    def __init__(self, image_path):
        """Initialize with the path to the stone wall image"""
        self.original_image = cv2.imread(image_path)
        if self.original_image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        # Convert to RGB for display purposes
        self.display_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
        
        # Will store the detected stones
        self.stones = []
        
        # Store image dimensions
        self.img_height, self.img_width = self.original_image.shape[:2]
        
        # Store preprocessed versions of the image
        self.preprocessed_images = {}

    def enhance_stone_boundaries(self, input_image, method='all'):

        """
        Enhanced boundary detection with advanced techniques
        
        Parameters:
        - input_image: Input image
        - method: Specific method to use or 'all' to try multiple methods
        
        Returns:
        Dictionary of enhanced images or a single enhanced image
        """
        def ensure_8bit_grayscale(image):
            """Ensure image is 8-bit grayscale"""
            if image.ndim > 2:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            if image.dtype != np.uint8:
                image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
            return image
        
        def safe_laplacian(image):
            """Apply Laplacian with noise reduction"""
            image = ensure_8bit_grayscale(image)
            # Denoise before Laplacian
            denoised = cv2.GaussianBlur(image, (3, 3), 0)
            # Apply Laplacian with larger kernel
            laplacian = cv2.Laplacian(denoised, cv2.CV_64F, ksize=5)
            # Convert back to 8-bit
            return cv2.normalize(laplacian, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Dictionary to store enhanced images
        enhanced = {}
        
        # Ensure input is 8-bit grayscale
        input_image = ensure_8bit_grayscale(input_image)
        
        # 1. Laplacian of Gaussian with stronger parameters
        enhanced['log'] = safe_laplacian(input_image)
        
        # 2. Difference of Gaussians with multiple scales
        def difference_of_gaussians(image, low_sigma=1, high_sigma=7):
            image = ensure_8bit_grayscale(image)
            low_blur = cv2.GaussianBlur(image, (0, 0), low_sigma)
            high_blur = cv2.GaussianBlur(image, (0, 0), high_sigma)
            dog = cv2.subtract(low_blur, high_blur)  # Note: low - high for edge enhancement
            return cv2.normalize(dog, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Apply DoG with multiple scales and combine
        dog1 = difference_of_gaussians(input_image, 1, 5)
        dog2 = difference_of_gaussians(input_image, 2, 7)
        dog3 = difference_of_gaussians(input_image, 3, 9)
        
        # Combine multiple DoG scales
        enhanced['dog'] = cv2.max(cv2.max(dog1, dog2), dog3)
        
        # 3. Sobel edge detection with gradient combination
        def sobel_edges(image):
            image = ensure_8bit_grayscale(image)
            # Apply Sobel in x and y directions with larger kernel
            sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=5)
            sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=5)
            # Calculate gradient magnitude
            magnitude = np.sqrt(sobelx**2 + sobely**2)
            # Normalize and convert to 8-bit
            return cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        enhanced['sobel'] = sobel_edges(input_image)
        
        # 4. Local Binary Pattern for texture-based edge detection
        def lbp_edges(image, radius=3):
            from skimage.feature import local_binary_pattern
            image = ensure_8bit_grayscale(image)
            # Number of points to consider
            n_points = 8 * radius
            # Compute LBP
            lbp = local_binary_pattern(image, n_points, radius, method='uniform')
            # Convert to 8-bit
            return cv2.normalize(lbp, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        try:
            enhanced['lbp'] = lbp_edges(input_image)
        except:
            # If skimage is not available
            enhanced['lbp'] = enhanced['sobel']
        
        # 5. Morphological gradient for edge detection
        def morphological_gradient(image):
            image = ensure_8bit_grayscale(image)
            kernel = np.ones((5, 5), np.uint8)
            # Apply morphological gradient
            return cv2.morphologyEx(image, cv2.MORPH_GRADIENT, kernel)
        
        enhanced['morph_gradient'] = morphological_gradient(input_image)
        
        # 6. Adaptive thresholding for local contrast enhancement
        def adaptive_threshold_edges(image):
            image = ensure_8bit_grayscale(image)
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY_INV, 21, 5
            )
            # Clean up with morphological operations
            kernel = np.ones((3, 3), np.uint8)
            return cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
        
        enhanced['adaptive_thresh'] = adaptive_threshold_edges(input_image)
        
        # 7. Canny edge detection with optimal thresholds
        def auto_canny(image, sigma=0.33):
            image = ensure_8bit_grayscale(image)
            # Compute median of the image
            v = np.median(image)
            # Apply automatic Canny edge detection using the median
            lower = int(max(0, (1.0 - sigma) * v))
            upper = int(min(255, (1.0 + sigma) * v))
            return cv2.Canny(image, lower, upper)
        
        enhanced['canny'] = auto_canny(input_image)
        
        # 8. Combined approach - weighted sum of multiple methods
        combined = np.zeros_like(input_image)
        weights = {
            'log': 0.15,
            'dog': 0.2,
            'sobel': 0.15,
            'lbp': 0.1,
            'morph_gradient': 0.15,
            'adaptive_thresh': 0.1,
            'canny': 0.15
        }
        
        for method_name, weight in weights.items():
            if method_name in enhanced:
                combined = cv2.addWeighted(combined, 1.0, enhanced[method_name], weight, 0)
        
        # Normalize the combined result
        enhanced['combined'] = cv2.normalize(combined, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Return the requested method or all methods
        if method != 'all' and method in enhanced:
            return enhanced[method]
        
        return enhanced
        
    # def enhance_stone_boundaries(self, input_image, method='all'):

    #     """
    #     Enhance stone boundaries using multiple advanced techniques
        
    #     Parameters:
    #     - input_image: Input image
    #     - method: Specific method to use or 'all' to try multiple methods
        
    #     Returns:
    #     Dictionary of enhanced images or a single enhanced image
    #     """
    #     def ensure_8bit_grayscale(image):
    #         """Ensure image is 8-bit grayscale"""
    #         # If color image, convert to grayscale
    #         if image.ndim > 2:
    #             image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
    #         # If not 8-bit, convert to 8-bit
    #         if image.dtype != np.uint8:
    #             # Normalize to 0-255 range
    #             image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
            
    #         return image

    #     def safe_laplacian(image):
    #         """Safe Laplacian edge detection"""
    #         # Ensure 8-bit grayscale
    #         image = ensure_8bit_grayscale(image)
            
    #         # Apply Gaussian blur to reduce noise
    #         blurred = cv2.GaussianBlur(image, (3, 3), 0)
            
    #         # Apply Laplacian
    #         try:
    #             # Ensure 8-bit output
    #             laplacian = cv2.Laplacian(blurred, cv2.CV_8U)
    #             return laplacian
    #         except Exception as e:
    #             print(f"Laplacian error: {e}")
    #             # Fallback method
    #             return cv2.Sobel(blurred, cv2.CV_8U, 1, 1)

    #     def safe_bilateral_laplacian(image):
    #         """Safe Bilateral filtering with Laplacian"""
    #         # Ensure 8-bit grayscale
    #         image = ensure_8bit_grayscale(image)
            
    #         try:
    #             # Apply bilateral filter
    #             bilateral = cv2.bilateralFilter(image, 9, 75, 75)
                
    #             # Ensure 8-bit Laplacian
    #             return safe_laplacian(bilateral)
    #         except Exception as e:
    #             print(f"Bilateral Laplacian error: {e}")
    #             # Fallback to standard Laplacian if bilateral fails
    #             return safe_laplacian(image)

    #     # Dictionary to store enhanced images
    #     enhanced = {}
        
    #     # Ensure input is 8-bit grayscale
    #     input_image = ensure_8bit_grayscale(input_image)
        
    #     # 1. Laplacian of Gaussian
    #     enhanced['log'] = safe_laplacian(input_image)
        
    #     # 2. Difference of Gaussians
    #     def difference_of_gaussians(image, low_sigma=1, high_sigma=5):
    #         image = ensure_8bit_grayscale(image)
    #         low_blur = cv2.GaussianBlur(image, (0, 0), low_sigma)
    #         high_blur = cv2.GaussianBlur(image, (0, 0), high_sigma)
            
    #         # Ensure 8-bit output
    #         dog = cv2.subtract(high_blur, low_blur)
    #         return cv2.normalize(dog, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
    #     enhanced['dog'] = difference_of_gaussians(input_image)
        
    #     # 3. Morphological gradient
    #     kernel = np.ones((3, 3), np.uint8)
    #     enhanced['morph_gradient'] = cv2.morphologyEx(input_image, cv2.MORPH_GRADIENT, kernel)
        
    #     # Add stronger morphological operations to close gaps
    #     enhanced['morph_gradient'] = cv2.morphologyEx(input_image, cv2.MORPH_GRADIENT, kernel)
        
    #     # Add morphological closing to connect nearby edges
    #     closing_kernel = np.ones((5, 5), np.uint8)
    #     enhanced['morph_close'] = cv2.morphologyEx(input_image, cv2.MORPH_CLOSE, closing_kernel)
        
    #     # 4. Multi-scale Canny
    #     def multi_scale_canny(image):
    #         image = ensure_8bit_grayscale(image)
    #         edges1 = cv2.Canny(image, 50, 150)
    #         edges2 = cv2.Canny(image, 100, 250)
    #         edges3 = cv2.Canny(image, 20, 100)
            
    #         # Combine edges
    #         combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
    #         return combined_edges
        
    #     enhanced['multi_canny'] = multi_scale_canny(input_image)
        
    #     # 5. Adaptive thresholding
    #     def adaptive_edge_preservation(image):
    #         image = ensure_8bit_grayscale(image)
    #         smoothed = cv2.bilateralFilter(image, 9, 75, 75)
    #         adaptive = cv2.adaptiveThreshold(
    #             smoothed, 255, 
    #             cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
    #             cv2.THRESH_BINARY_INV, 
    #             11, 2
    #         )
    #         return adaptive
        
    #     enhanced['adaptive_edge_pres'] = adaptive_edge_preservation(input_image)
        
    #     # 6. Sobel edge detection
    #     def sobel_edges(image):
    #         image = ensure_8bit_grayscale(image)
    #         sobelx = cv2.Sobel(image, cv2.CV_8U, 1, 0, ksize=3)
    #         sobely = cv2.Sobel(image, cv2.CV_8U, 0, 1, ksize=3)
    #         sobel_combined = cv2.addWeighted(sobelx, 0.5, sobely, 0.5, 0)
    #         return sobel_combined
        
    #     enhanced['sobel'] = sobel_edges(input_image)
        
    #     # 7. Bilateral Laplacian
    #     enhanced['bilateral_laplacian'] = safe_bilateral_laplacian(input_image)
        
    #     # 8. CLAHE with morphological gradient
    #     clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    #     clahe_img = clahe.apply(input_image)
    #     enhanced['clahe_morph_grad'] = cv2.morphologyEx(clahe_img, cv2.MORPH_GRADIENT, kernel)
        
    #     # 9. Unsharp masking with gradient
    #     def unsharp_gradient(image):
    #         image = ensure_8bit_grayscale(image)
    #         blurred = cv2.GaussianBlur(image, (0, 0), 3)
    #         unsharp = cv2.addWeighted(image, 1.5, blurred, -0.5, 0)
    #         return cv2.morphologyEx(unsharp, cv2.MORPH_GRADIENT, kernel)
        
    #     enhanced['unsharp_gradient'] = unsharp_gradient(input_image)
        
    #     # If a specific method is requested, return that
    #     if method != 'all' and method in enhanced:
    #         return enhanced[method]
        
    #     return enhanced

    def preprocess_image(self, visualize=False):
        """Extended preprocessing with saturation/highlight adjustment and boundary enhancement"""
        # Initialize preprocessed images dictionary
        preprocessed = {}
        
        # First, apply saturation and highlight adjustment
        adjusted_image = self.adjust_saturation_highlights(
            self.original_image, 
            saturation_factor=-100,  # Decrease saturation by 80
            highlight_factor=100     # Increase highlights by 80
        )
        
        # Store the adjusted image
        preprocessed['adjusted'] = cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2GRAY)
        
        # Use the adjusted image for further processing
        gray_adjusted = cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2GRAY)
        preprocessed['original'] = gray_adjusted
        
        # Also keep the original grayscale for comparison
        gray_original = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        preprocessed['original_unmodified'] = gray_original
        
        # Safe image combination function
        def safe_combine_images(img1, img2):
            """Safely combine two images of potentially different sizes"""
            # Ensure both images are grayscale and same type
            if img1.ndim > 2:
                img1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
            if img2.ndim > 2:
                img2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
            
            # Resize img2 to match img1 if sizes differ
            if img1.shape != img2.shape:
                img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]), 
                                interpolation=cv2.INTER_LINEAR)
            
            # Ensure same data type
            img2 = img2.astype(img1.dtype)
            
            # Combine images
            return cv2.bitwise_or(img1, img2)
        
        # Add boundary enhanced versions using the adjusted image
        try:
            boundary_methods = self.enhance_stone_boundaries(adjusted_image)
            preprocessed.update(boundary_methods)
        except Exception as e:
            print(f"Error in boundary enhancement: {e}")
        
        # Create additional combinations
        # 1. CLAHE processing
        try:
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))  # Increased clip limit
            clahe_img = clahe.apply(gray_adjusted)
            preprocessed['clahe'] = clahe_img
            
            # Combine CLAHE with boundary methods
            for method_name, boundary_img in boundary_methods.items():
                combined_key = f'clahe_{method_name}'
                try:
                    preprocessed[combined_key] = safe_combine_images(clahe_img, boundary_img)
                except Exception as e:
                    print(f"Error combining CLAHE with {method_name}: {e}")
        except Exception as e:
            print(f"Error in CLAHE processing: {e}")
        
        # 2. Bilateral filtering with stronger parameters
        try:
            bilateral = cv2.bilateralFilter(gray_adjusted, 11, 100, 100)  # Stronger filtering
            preprocessed['bilateral'] = bilateral
            
            # Combine bilateral with boundary methods
            for method_name, boundary_img in boundary_methods.items():
                combined_key = f'bilateral_{method_name}'
                try:
                    preprocessed[combined_key] = safe_combine_images(bilateral, boundary_img)
                except Exception as e:
                    print(f"Error combining bilateral with {method_name}: {e}")
        except Exception as e:
            print(f"Error in bilateral processing: {e}")
        
        # 3. Add adaptive thresholding
        try:
            adaptive_thresh = cv2.adaptiveThreshold(
                gray_adjusted, 255, 
                cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY_INV, 
                15, 3  # Larger block size and constant
            )
            preprocessed['adaptive_thresh'] = adaptive_thresh
        except Exception as e:
            print(f"Error in adaptive thresholding: {e}")
        
        # Visualization
        if visualize:
            try:
                rows = int(np.ceil(len(preprocessed) / 4))
                cols = min(len(preprocessed), 4)
                
                plt.figure(figsize=(16, 4 * rows))
                for i, (name, img) in enumerate(preprocessed.items()):
                    plt.subplot(rows, cols, i + 1)
                    plt.imshow(img, cmap='gray')
                    plt.title(name)
                    plt.axis('off')
                
                plt.tight_layout()
                plt.show()
            except Exception as e:
                print(f"Visualization error: {e}")
        
        # Store preprocessed images for future use
        self.preprocessed_images = preprocessed
        
        # Also update the display image to use the adjusted version
        self.display_image = adjusted_image
        
        return preprocessed

    def detect_mortar_lines(self, low_threshold=30, high_threshold=200, mortar_brightness=None):
        """
        Detect mortar lines between stones with improved parameters
        
        Parameters:
        - low_threshold: Lower threshold for Canny edge detection
        - high_threshold: Upper threshold for Canny edge detection
        - mortar_brightness: Optional tuple (min, max) for brightness-based mortar detection
        """
        # Use the adjusted image if available, otherwise convert original to grayscale
        if hasattr(self, 'preprocessed_images') and 'adjusted' in self.preprocessed_images:
            gray = self.preprocessed_images['adjusted']
        else:
            gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE with stronger parameters
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Use bilateral filter with stronger parameters
        bilateral = cv2.bilateralFilter(enhanced, 11, 100, 100)
        
        # Create a mask for potential mortar lines based on brightness if specified
        mortar_mask = None
        if mortar_brightness is not None:
            min_val, max_val = mortar_brightness
            # Assume mortar is typically lighter or darker than stones
            mortar_mask = cv2.inRange(bilateral, min_val, max_val)
            
            # Clean up the mask with stronger morphological operations
            kernel = np.ones((3, 3), np.uint8)
            mortar_mask = cv2.morphologyEx(mortar_mask, cv2.MORPH_OPEN, kernel, iterations=2)
            mortar_mask = cv2.morphologyEx(mortar_mask, cv2.MORPH_CLOSE, kernel, iterations=3)
        
        # Use Canny edge detection with adjusted thresholds
        edges = cv2.Canny(bilateral, low_threshold, high_threshold)
        
        # Dilate edges more aggressively
        kernel = np.ones((3, 3), np.uint8)  # Larger kernel
        dilated_edges = cv2.dilate(edges, kernel, iterations=2)  # More iterations
        
        # If we have a mortar mask, combine it with edge detection
        if mortar_mask is not None:
            combined = cv2.bitwise_or(dilated_edges, mortar_mask)
            return combined
        
        return dilated_edges

    def find_stone_contours(self, mortar_lines=None, adaptive_threshold=True):
        """
        Find contours of stones using mortar lines to separate them
        
        Parameters:
        - mortar_lines: Pre-computed mortar lines image (if None, will be computed)
        - adaptive_threshold: Whether to use adaptive thresholding for extra segmentation
        """
        # Get mortar lines if not provided
        if mortar_lines is None:
            mortar_lines = self.detect_mortar_lines()
        
        # Invert the image so stones are white and mortar lines are black
        inverted = 255 - mortar_lines
        
        # Apply stronger morphological operations to connect stone regions
        kernel = np.ones((7, 7), np.uint8)  # Larger kernel
        closed = cv2.morphologyEx(inverted, cv2.MORPH_CLOSE, kernel, iterations=3)  # More iterations
        
        # Fill holes in stone regions
        filled_mask = np.zeros_like(closed)
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter and fill contours with more lenient criteria
        filtered_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 50:  # Lower threshold to catch more potential stones
                continue
            cv2.drawContours(filled_mask, [contour], 0, 255, -1)
            filtered_contours.append(contour)
        
        return filled_mask, filtered_contours

    def watershed_segmentation(self):
        """Use watershed algorithm to segment stones that are touching"""
        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE to enhance contrast
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Apply threshold with more aggressive parameters
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
        
        # Remove noise with larger kernel
        kernel = np.ones((5, 5), np.uint8)
        opening = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=2)
        
        # Sure background area - more dilation
        sure_bg = cv2.dilate(opening, kernel, iterations=4)
        
        # Finding sure foreground area - more aggressive threshold
        dist_transform = cv2.distanceTransform(opening, cv2.DIST_L2, 5)
        _, sure_fg = cv2.threshold(dist_transform, 0.4*dist_transform.max(), 255, 0)
        sure_fg = np.uint8(sure_fg)
        
        # Finding unknown region
        unknown = cv2.subtract(sure_bg, sure_fg)
        
        # Marker labelling
        _, markers = cv2.connectedComponents(sure_fg)
        
        # Add one to all labels so that background is not 0, but 1
        markers = markers + 1
        
        # Mark the unknown region with zero
        markers[unknown == 255] = 0
        
        # Apply watershed
        markers = cv2.watershed(self.original_image, markers)
        
        # Create a mask where boundaries are marked
        boundary_mask = np.zeros_like(gray)
        boundary_mask[markers == -1] = 255
        
        # Dilate boundaries to ensure separation
        boundary_mask = cv2.dilate(boundary_mask, kernel, iterations=1)
        
        # Invert to get regions
        stone_regions = 255 - boundary_mask
        
        # Find contours in the resulting mask
        contours, _ = cv2.findContours(stone_regions, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        return stone_regions, contours

    def try_multiple_approaches(self, preprocess_method=None, visualize=False):
        """
        Try multiple approaches to find the best stone segmentation
        
        Parameters:
        - preprocess_method: Name of preprocessing method to use (if None, uses original image)
        - visualize: Whether to show visualization of results
        """
        # If preprocessed image is specified, use it
        if preprocess_method and preprocess_method in self.preprocessed_images:
            # Create a temporary BGR image for OpenCV functions that expect color
            temp_img = cv2.cvtColor(self.preprocessed_images[preprocess_method], cv2.COLOR_GRAY2BGR)
            
            # Store original image
            orig_img = self.original_image
            
            # Replace original image with preprocessed version temporarily
            self.original_image = temp_img
        
        # Approach 1: Standard edge-based detection
        mortar_lines1 = self.detect_mortar_lines(low_threshold=30, high_threshold=150)
        mask1, contours1 = self.find_stone_contours(mortar_lines1)
        
        # Approach 2: More aggressive edge detection
        mortar_lines2 = self.detect_mortar_lines(low_threshold=20, high_threshold=200)
        mask2, contours2 = self.find_stone_contours(mortar_lines2)
        
        # Approach 3: Try detecting dark mortar
        mortar_lines3 = self.detect_mortar_lines(low_threshold=30, high_threshold=150, 
                                                mortar_brightness=(0, 100))
        mask3, contours3 = self.find_stone_contours(mortar_lines3)
        
        # Approach 4: Try detecting light mortar
        mortar_lines4 = self.detect_mortar_lines(low_threshold=30, high_threshold=150, 
                                                mortar_brightness=(150, 255))
        mask4, contours4 = self.find_stone_contours(mortar_lines4)
        
        # Approach 5: Watershed segmentation
        mask5, contours5 = self.watershed_segmentation()
        
        # Restore original image if we used a preprocessed version
        if preprocess_method and preprocess_method in self.preprocessed_images:
            self.original_image = orig_img
        
        # Filter contours to ensure quality
        filtered_contours1 = self.filter_contours(contours1)
        filtered_contours2 = self.filter_contours(contours2)
        filtered_contours3 = self.filter_contours(contours3)
        filtered_contours4 = self.filter_contours(contours4)
        filtered_contours5 = self.filter_contours(contours5)
        
        # Choose the approach that produced the most reasonable number of contours
        approaches = [
            (len(filtered_contours1), filtered_contours1, mask1, f"Edge-based detection ({preprocess_method})"),
            (len(filtered_contours2), filtered_contours2, mask2, f"Aggressive edge detection ({preprocess_method})"),
            (len(filtered_contours3), filtered_contours3, mask3, f"Dark mortar detection ({preprocess_method})"),
            (len(filtered_contours4), filtered_contours4, mask4, f"Light mortar detection ({preprocess_method})"),
            (len(filtered_contours5), filtered_contours5, mask5, f"Watershed segmentation ({preprocess_method})")
        ]
        
        # Sort by number of contours, but exclude approaches with too many contours (likely noise)
        valid_approaches = [(count, contours, mask, name) for count, contours, mask, name in approaches 
                          if 1 < count < 1000]  # Adjust limits based on expected number of stones
        
        if not valid_approaches:
            # If all approaches gave bad results, take the one with at least some contours
            valid_approaches = [(count, contours, mask, name) for count, contours, mask, name in approaches 
                              if count > 1]
        
        if not valid_approaches:
            # Last resort - take any approach with at least one contour
            valid_approaches = [(count, contours, mask, name) for count, contours, mask, name in approaches 
                              if count > 0]
        
        if not valid_approaches:
            raise ValueError("Could not detect any stones with any approach")
        
        # Choose approach with the most contours (most stones detected)
        best_approach = max(valid_approaches, key=lambda x: x[0])
        best_count, best_contours, best_mask, best_name = best_approach
        
        print(f"Selected approach: {best_name} with {best_count} stones")
        
        if visualize:
            # Show all approaches
            plt.figure(figsize=(15, 15))
            
            for i, (count, _, mask, name) in enumerate(approaches):
                plt.subplot(2, 3, i+1)
                plt.imshow(mask, cmap='gray')
                plt.title(f"{name}\n{count} stones")
                plt.axis('off')
            
            plt.subplot(2, 3, 6)
            vis_img = self.display_image.copy()
            contour_img = cv2.drawContours(vis_img, best_contours, -1, (0, 255, 0), 2)
            plt.imshow(contour_img)
            plt.title(f"Selected: {best_name}")
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
        
        return best_mask, best_contours

    def filter_contours(self, contours, min_area=200, max_aspect_ratio=8.0, convexity_threshold=0.7):
        """Filter contours based on various criteria to ensure quality"""
        filtered = []
        for contour in contours:
            # Get area of the contour
            area = cv2.contourArea(contour)
            
            # Skip very small contours (likely noise)
            if area < min_area:
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check aspect ratio to avoid extremely elongated shapes
            aspect_ratio = max(w, h) / max(1, min(w, h))
            if aspect_ratio > max_aspect_ratio:
                continue
            
            # Check convexity (ratio of contour area to its convex hull area)
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            if hull_area > 0:
                convexity = area / hull_area
                if convexity < convexity_threshold:
                    continue
            
            filtered.append(contour)
        
        return filtered
        
    def find_best_preprocessing(self, visualize=False):
        """Try all preprocessing methods and find the one that detects the most stones"""
        # First, apply all preprocessing methods
        preprocessed = self.preprocess_image(visualize=visualize)
        
        best_method = None
        best_count = 0
        best_contours = None
        best_mask = None
        best_approach_name = None
        
        results = []
        
        # Try original image first
        print("Testing stone detection on original image...")
        mask, contours = self.try_multiple_approaches(preprocess_method=None, visualize=False)
        count = len(contours)
        
        if count > best_count:
            best_count = count
            best_contours = contours
            best_mask = mask
            best_method = "original"
        
        results.append((count, "original"))
        
        # Try each preprocessing method
        for method_name in preprocessed.keys():
            print(f"Testing stone detection with {method_name} preprocessing...")
            mask, contours = self.try_multiple_approaches(preprocess_method=method_name, visualize=False)
            count = len(contours)
            
            results.append((count, method_name))
            
            if count > best_count:
                best_count = count
                best_contours = contours
                best_mask = mask
                best_method = method_name
        
        # Sort results by stone count (descending)
        results.sort(reverse=True)
        
        print("\nPreprocessing method comparison:")
        print("--------------------------------")
        for count, method in results:
            star = " *" if method == best_method else ""
            print(f"{method:<20}: {count} stones{star}")
        
        print(f"\nBest preprocessing method: {best_method} with {best_count} stones detected")
        
        if visualize and best_method:
            # Show original vs best preprocessed
            plt.figure(figsize=(15, 10))
            
            plt.subplot(2, 2, 1)
            plt.imshow(self.display_image)
            plt.title("Original Image")
            plt.axis('off')
            
            plt.subplot(2, 2, 2)
            if best_method == "original":
                plt.imshow(cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB))
            else:
                plt.imshow(self.preprocessed_images[best_method], cmap='gray')
            plt.title(f"Best Preprocessing: {best_method}")
            plt.axis('off')
            
            # Create visualization image
            vis_img = self.display_image.copy()
            
            # Draw contours on the visualization image
            contour_img = cv2.drawContours(vis_img, contours, -1, (0, 255, 0), 2)
            
            plt.subplot(2, 2, 3)
            plt.imshow(mask, cmap='gray')
            plt.title("Stone Mask")
            plt.axis('off')
            
            plt.subplot(2, 2, 4)
            plt.imshow(contour_img)
            plt.title(f"Detected Stones: {best_count}")
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
        
        return best_method, best_mask, best_contours
    
    def extract_stones(self, min_area=200, max_area=500000, max_aspect_ratio=6.0, visualize=False):

        """
        Extract individual stones with minimal filtering to ensure larger stones are included
        
        Parameters:
        - min_area: Minimum stone area in pixels
        - max_area: Maximum stone area in pixels (greatly increased)
        - max_aspect_ratio: Maximum aspect ratio for stone detection (very lenient)
        - visualize: Whether to visualize the extraction process
        
        Returns:
        - List of extracted stones
        """
        # Reset stones list
        self.stones = []
        
        # Find the best preprocessing method and get stone segmentation
        best_method, filled_mask, contours = self.find_best_preprocessing(visualize=False)
        
        print(f"Best preprocessing method: {best_method} with {len(contours)} stones detected")
        
        # Store original contours for visualization
        original_contours = contours.copy()
        
        # Create a list to store all candidate stones before filtering
        candidate_stones = []
        rejected_stones = {'aspect_ratio': 0, 'size': 0, 'other': 0}
        
        # Process each contour to extract a stone
        for i, contour in enumerate(contours):
            # Get area of the contour
            area = cv2.contourArea(contour)
            
            # Skip very small contours (likely noise)
            if area < min_area:
                rejected_stones['size'] += 1
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check aspect ratio - be extremely lenient with larger stones
            aspect_ratio = max(w, h) / max(1, min(w, h))
            # For large stones, allow even higher aspect ratios
            adjusted_max_aspect = max_aspect_ratio * 2.0 if area > 10000 else max_aspect_ratio
            if aspect_ratio > adjusted_max_aspect:
                rejected_stones['aspect_ratio'] += 1
                continue
            
            # Create a mask for this stone
            stone_mask = np.zeros((self.img_height, self.img_width), dtype=np.uint8)
            cv2.drawContours(stone_mask, [contour], 0, 255, -1)
            
            # Extract the region of interest
            roi_mask = stone_mask[y:y+h, x:x+w]
            roi_image = self.original_image[y:y+h, x:x+w]
            
            # Create transparent image for the stone
            stone_img = np.zeros((h, w, 4), dtype=np.uint8)
            
            # Copy stone pixels
            if roi_image.ndim == 3:
                # Set RGB channels
                stone_img[:, :, 0:3] = np.where(
                    roi_mask[:, :, np.newaxis] == 255, 
                    roi_image, 
                    stone_img[:, :, 0:3]
                )
            else:
                # Handle grayscale images
                stone_img[:, :, 0:3] = np.where(
                    roi_mask[:, :, np.newaxis] == 255, 
                    np.stack([roi_image]*3, axis=2), 
                    stone_img[:, :, 0:3]
                )
            
            # Set alpha channel
            stone_img[:, :, 3] = roi_mask
            
            # Store stone info
            stone_info = {
                'image': stone_img,
                'width': w,
                'height': h,
                'area': area,
                'mask': roi_mask.copy(),
                'aspect_ratio': aspect_ratio,
                'x': x,
                'y': y
            }
            
            # Add to candidate stones
            candidate_stones.append(stone_info)
        
        print(f"Found {len(candidate_stones)} candidate stones before filtering")
        
        # First, automatically include all large stones
        for stone in candidate_stones:
            if stone['area'] > 20000:  # Automatically include all large stones
                self.stones.append(stone)
        
        print(f"Automatically included {len(self.stones)} large stones (area > 20000)")
        
        # Then apply validation to filter the remaining stones
        large_stone_ids = {id(stone) for stone in self.stones}  # Track already included stones
        
        for stone in candidate_stones:
            # Skip stones that were already included
            if id(stone) in large_stone_ids:
                continue
                
            # Check if stone is valid using very lenient criteria
            if self.is_valid_stone(stone, min_area=min_area, max_area=max_area):
                self.stones.append(stone)
            else:
                rejected_stones['other'] += 1
        
        # Sort stones by area (largest first)
        self.stones.sort(key=lambda s: s['area'], reverse=True)
        
        # Print rejection statistics
        print(f"Rejected stones statistics:")
        for reason, count in rejected_stones.items():
            print(f"  - {reason}: {count}")
        
        print(f"Extracted {len(self.stones)} valid stones after filtering")
        
        # If no stones were found, try with extremely lenient parameters
        if len(self.stones) == 0:
            print("No stones passed filtering. Including all candidate stones...")
            # Include all candidate stones
            self.stones = candidate_stones
            print(f"Included all {len(self.stones)} candidate stones")
        
        # Visualize the extraction process if requested
        if visualize and len(self.stones) > 0:
            self.display_extracted_stones(max_stones=30)
        
        return self.stones

    def display_extracted_stones(self, max_stones=30):
        """
        Display the extracted stones in a grid
        
        Parameters:
        - max_stones: Maximum number of stones to display
        """
        if len(self.stones) == 0:
            print("No stones to display")
            return
        
        # Determine how many stones to show
        stones_to_show = min(max_stones, len(self.stones))
        
        # Calculate grid dimensions
        grid_cols = min(5, stones_to_show)
        grid_rows = (stones_to_show + grid_cols - 1) // grid_cols
        
        # Create figure
        plt.figure(figsize=(15, 3 * grid_rows))
        
        # Display stones
        for i in range(stones_to_show):
            stone = self.stones[i]
            plt.subplot(grid_rows, grid_cols, i + 1)
            
            # Display stone with alpha channel
            plt.imshow(stone['image'])
            plt.title(f"Stone {i+1}\nArea: {stone['area']}\n{stone['width']}x{stone['height']}")
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Also show the original image with stone contours
        plt.figure(figsize=(12, 10))
        plt.imshow(cv2.cvtColor(self.display_image, cv2.COLOR_BGR2RGB))
        
        # Draw contours of valid stones
        for stone in self.stones:
            # Create a temporary mask for this stone
            temp_mask = np.zeros((self.img_height, self.img_width), dtype=np.uint8)
            x, y = stone['x'], stone['y']
            temp_mask[y:y+stone['height'], x:x+stone['width']] = stone['mask']
            
            # Find contours of this stone in the full image
            contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Draw contours
            for contour in contours:
                # Convert contour to list of points for matplotlib
                contour_points = contour.reshape(-1, 2)
                plt.plot(contour_points[:, 0], contour_points[:, 1], 'g', linewidth=2)
        
        plt.title(f"Original Image with {len(self.stones)} Valid Stones")
        plt.axis('off')
        plt.tight_layout()
        plt.show()

    def generate_wall(self, width=1000, height=800, bg_color=(200, 200, 200), gap=5, visualize=False):
        """
        Generate a stone wall image using the extracted stones in a randomized Tetris-like arrangement
        
        Parameters:
        - width: Width of the output wall image
        - height: Height of the output wall image
        - bg_color: Background color (mortar color) as RGB tuple
        - gap: Gap between stones in pixels
        - visualize: Whether to show the generated wall
        
        Returns:
        - Wall image as numpy array
        """
        if not self.stones:
            self.extract_stones()
        
        # Filter out invalid stones
        valid_stones = [s for s in self.stones if self.is_valid_stone(s)]
        
        if len(valid_stones) == 0:
            print("No valid stones were extracted. Try adjusting the parameters.")
            return None
        
        print(f"Using {len(valid_stones)} valid stones for wall generation")
        
        # Create a blank image for the wall with background color
        wall_image = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(wall_image)
        
        # Create a height map to track the current height at each x position
        height_map = np.zeros(width, dtype=int)
        
        # Sort stones by area (largest first) to prioritize placing larger stones
        stones_by_size = sorted(valid_stones, key=lambda s: s['area'], reverse=True)
        
        # Keep track of placed stones to avoid repetitive patterns
        recently_used = []
        max_recent = min(10, len(valid_stones) // 3)  # Avoid reusing the same stones too frequently
        
        # Place stones until we fill the wall or run out of valid positions
        attempts = 0
        max_attempts = len(valid_stones) * 20  # Avoid infinite loops
        stones_placed = 0
        
        while attempts < max_attempts:
            # Find several low points in the height map and randomly choose one
            # This adds randomness while still maintaining the Tetris-like stacking
            lowest_indices = np.argsort(height_map)[:min(20, width//10)]
            min_height_x = np.random.choice(lowest_indices)
            current_y = height_map[min_height_x]
            
            # If we've filled the wall, we're done
            if current_y >= height:
                break
            
            # Randomly select a stone from the available ones, with preference for larger stones
            # This adds more randomness to the stone selection
            available_stones = [i for i, stone in enumerate(stones_by_size) if i not in recently_used]
            
            if not available_stones:
                # If all stones were recently used, reset the recently used list
                recently_used = []
                available_stones = list(range(len(stones_by_size)))
            
            # Weight selection toward larger stones but with randomness
            weights = [1.0 / (i + 1) for i in range(len(available_stones))]
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]
            
            # Randomly select a stone index based on weights
            stone_idx = np.random.choice(available_stones, p=weights)
            selected_stone = stones_by_size[stone_idx]
            
            stone_width = selected_stone['width']
            stone_height = selected_stone['height']
            
            # Check if stone would fit horizontally
            if min_height_x + stone_width > width:
                # Try to place it further left
                min_height_x = max(0, width - stone_width)
            
            # Place the selected stone
            stone_pil = Image.fromarray(selected_stone['image'])
            wall_image.paste(stone_pil, (min_height_x, current_y), stone_pil.split()[3])
            
            # Update the height map for the area covered by this stone
            for x in range(min_height_x, min(width, min_height_x + stone_width)):
                height_map[x] = current_y + stone_height + gap
            
            # Add this stone to recently used list
            recently_used.append(stone_idx)
            if len(recently_used) > max_recent:
                recently_used.pop(0)  # Remove oldest stone from recently used
            
            stones_placed += 1
            attempts += 1
            
            # Occasionally add some randomness to the height map to create more interesting patterns
            if random.random() < 0.1:  # 10% chance
                # Add random bumps to the height map
                bump_start = random.randint(0, width - 50)
                bump_width = random.randint(20, 50)
                bump_height = random.randint(10, 30)
                height_map[bump_start:bump_start+bump_width] += bump_height
            
            # Every 20 stones, check if we need to fill gaps
            if stones_placed % 20 == 0:
                # Find significant gaps in the height map
                for x in range(1, width-1):
                    # If there's a significant dip in the height map, try to fill it
                    if (height_map[x] < height_map[x-1] - 30 and 
                        height_map[x] < height_map[x+1] - 30):
                        # Find a small stone that fits this gap
                        for stone in reversed(stones_by_size):  # Try smaller stones first
                            if stone['width'] < 50 and stone['height'] < 50:
                                stone_pil = Image.fromarray(stone['image'])
                                wall_image.paste(stone_pil, (x, height_map[x]), stone_pil.split()[3])
                                # Update height map
                                for gap_x in range(x, min(width, x + stone['width'])):
                                    if gap_x < width:
                                        height_map[gap_x] = height_map[x] + stone['height'] + gap
                                break
        
        print(f"Placed {stones_placed} stones in the wall")
        
        # Fill any remaining gaps with small stones
        # Find significant gaps in the height map
        for x in range(5, width-5, 5):  # Check every 5 pixels
            # If there's a significant dip in the height map
            local_min = np.min(height_map[max(0, x-10):min(width, x+10)])
            local_max = np.max(height_map[max(0, x-10):min(width, x+10)])
            
            if local_max - local_min > 40 and height_map[x] < local_max - 30:
                # Find a small stone that fits this gap
                for stone in reversed(stones_by_size):  # Try smaller stones first
                    if stone['width'] < 40 and stone['height'] < 40:
                        gap_y = height_map[x]
                        stone_pil = Image.fromarray(stone['image'])
                        wall_image.paste(stone_pil, (x, gap_y), stone_pil.split()[3])
                        # Update height map
                        for gap_x in range(x, min(width, x + stone['width'])):
                            if gap_x < width:
                                height_map[gap_x] = max(height_map[gap_x], gap_y + stone['height'] + gap)
                        break
        
        wall_array = np.array(wall_image)
        
        if visualize:
            plt.figure(figsize=(10, 8))
            plt.imshow(wall_array)
            plt.title("Generated Stone Wall (Random Tetris Style)")
            plt.axis('off')
            
            # Also show the height map
            plt.figure(figsize=(10, 3))
            plt.plot(height_map)
            plt.title("Wall Height Map")
            plt.xlabel("X Position")
            plt.ylabel("Height")
            plt.show()
        
        return wall_array

    def save_wall(self, output_path, width=1000, height=800, bg_color=(200, 200, 200), gap=2):
        """Generate and save a Tetris-style stone wall to the specified path"""
        # Generate the wall (returns a numpy array)
        wall_array = self.generate_wall(width, height, bg_color, gap, visualize=False)
        
        if wall_array is None:
            print("Failed to generate wall")
            return None
        
        # Convert numpy array to PIL Image
        wall_image = Image.fromarray(wall_array)
        
        # Save the image
        wall_image.save(output_path)
        print(f"Tetris-style stone wall saved to {output_path}")
        return wall_array

    def extend_partial_stones(self, stone_mask, original_image, max_iterations=5):
        """
        Enhanced method to extend partial stones to their likely full shape
        
        Parameters:
        - stone_mask: Binary mask of the detected stone
        - original_image: Original image to sample texture from
        - max_iterations: Maximum number of dilation iterations
        
        Returns:
        - Extended stone mask
        """
        # Convert to grayscale if needed
        if len(original_image.shape) > 2:
            gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
        else:
            gray_image = original_image.copy()
        
        # Create a copy of the mask
        extended_mask = stone_mask.copy()
        
        # Calculate texture statistics for the current stone
        stone_pixels = gray_image[stone_mask > 0]
        if len(stone_pixels) == 0:
            return stone_mask
        
        mean_intensity = np.mean(stone_pixels)
        std_intensity = np.std(stone_pixels)
        
        # Create a kernel for dilation
        kernel = np.ones((3, 3), np.uint8)
        
        # Adaptive similarity threshold based on texture variance
        # More variance = more lenient threshold
        base_threshold = 2.0
        similarity_threshold = base_threshold * (1 + std_intensity / 50)
        
        # Check if the stone is likely partial (touching the border)
        h, w = stone_mask.shape
        border_pixels = np.sum(stone_mask[0, :]) + np.sum(stone_mask[-1, :]) + \
                       np.sum(stone_mask[:, 0]) + np.sum(stone_mask[:, -1])
        
        is_partial = border_pixels > 0
        
        # If the stone is partial, be more aggressive with extension
        if is_partial:
            max_iterations += 3
            similarity_threshold *= 1.5
        
        # Iteratively extend the stone
        for i in range(max_iterations):
            # Dilate the current mask
            dilated = cv2.dilate(extended_mask, kernel, iterations=1)
            
            # Find new pixels (in dilated but not in current mask)
            new_pixels_mask = cv2.subtract(dilated, extended_mask)
            
            # If no new pixels, stop
            if np.sum(new_pixels_mask) == 0:
                break
            
            # Check texture similarity for new pixels
            new_pixel_coords = np.where(new_pixels_mask > 0)
            new_pixel_values = gray_image[new_pixel_coords]
            
            # Calculate z-scores for new pixels
            z_scores = np.abs(new_pixel_values - mean_intensity) / (std_intensity + 1e-5)
            
            # Create mask of similar pixels
            similar_pixels = z_scores < similarity_threshold
            
            # If no similar pixels, stop
            if np.sum(similar_pixels) == 0:
                break
            
            # Create a mask of the similar new pixels
            similar_mask = np.zeros_like(new_pixels_mask)
            similar_coords = (new_pixel_coords[0][similar_pixels], new_pixel_coords[1][similar_pixels])
            similar_mask[similar_coords] = 255
            
            # Add similar pixels to the extended mask
            extended_mask = cv2.bitwise_or(extended_mask, similar_mask)
            
            # Update texture statistics (rolling average)
            stone_pixels = gray_image[extended_mask > 0]
            if len(stone_pixels) > 0:
                mean_intensity = 0.7 * mean_intensity + 0.3 * np.mean(stone_pixels)
                std_intensity = 0.7 * std_intensity + 0.3 * np.std(stone_pixels)
        
        # Final cleanup with morphological operations
        extended_mask = cv2.morphologyEx(extended_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
        
        return extended_mask

    def is_valid_stone(self, stone, min_area=200, max_area=500000, min_area_ratio=0.3, min_fullness=0.5):
        """
        Extremely lenient validation to filter out only the most obvious partial stones and multi-stone instances
        
        Parameters:
        - stone: Stone dictionary containing image, mask, etc.
        - min_area: Minimum absolute area in pixels
        - max_area: Maximum absolute area in pixels (greatly increased)
        - min_area_ratio: Minimum ratio of stone area to bounding box area (very lenient)
        - min_fullness: Minimum ratio of stone area to convex hull area (very lenient)
        
        Returns:
        - Boolean indicating if the stone is valid
        """
        # Check minimum area only - be extremely lenient with maximum area
        if stone['area'] < min_area:
            return False
        
        # For very large stones, skip most checks and just accept them
        if stone['area'] > 20000:
            # Only check if it's not extremely elongated
            if stone['aspect_ratio'] > 8.0:
                return False
            return True
        
        # For medium and small stones, apply some basic checks
        
        # Get the mask
        mask = stone['mask']
        
        # Check area ratio (stone area / bounding box area) - very lenient
        bbox_area = stone['width'] * stone['height']
        area_ratio = stone['area'] / max(1, bbox_area)
        if area_ratio < min_area_ratio:
            return False
        
        # Check fullness (stone area / convex hull area) - very lenient
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) == 0:
            return False
        
        hull = cv2.convexHull(contours[0])
        hull_mask = np.zeros_like(mask)
        cv2.drawContours(hull_mask, [hull], 0, 255, -1)
        
        hull_area = np.sum(hull_mask > 0)
        fullness = stone['area'] / max(1, hull_area)
        if fullness < min_fullness:
            return False
        
        # Check for extremely fragmented regions (likely multiple stones)
        num_labels, _ = cv2.connectedComponents(mask)
        if num_labels > 10:  # Very lenient - allow up to 9 disconnected regions
            return False
        
        # Check circularity only for small stones
        if stone['area'] < 5000:
            perimeter = cv2.arcLength(contours[0], True)
            circularity = 4 * np.pi * stone['area'] / (perimeter * perimeter + 1e-6)
            if circularity < 0.15:  # Extremely lenient
                return False
        
        return True

    def separate_connected_stones(self, mask):
        """
        Advanced method to separate connected stones using multiple techniques
        
        Parameters:
        - mask: Binary mask containing potentially connected stones
        
        Returns:
        - List of masks, each containing a single stone
        """
        # Make a copy of the mask
        mask_copy = mask.copy()
        
        # Step 1: Apply distance transform to find stone centers
        dist_transform = cv2.distanceTransform(mask_copy, cv2.DIST_L2, 5)
        
        # Step 2: Find local maxima in the distance transform (stone centers)
        # Normalize distance transform
        cv2.normalize(dist_transform, dist_transform, 0, 1.0, cv2.NORM_MINMAX)
        
        # Apply multiple thresholds to find peaks at different levels
        peaks_masks = []
        for thresh in [0.7, 0.6, 0.5, 0.4]:
            _, peaks = cv2.threshold(dist_transform, thresh, 1.0, cv2.THRESH_BINARY)
            peaks = np.uint8(peaks * 255)
            # Clean up noise in local maxima
            kernel = np.ones((3, 3), np.uint8)
            peaks = cv2.morphologyEx(peaks, cv2.MORPH_OPEN, kernel, iterations=1)
            peaks_masks.append(peaks)
        
        # Combine peaks from different thresholds
        combined_peaks = np.zeros_like(peaks_masks[0])
        for peaks in peaks_masks:
            combined_peaks = cv2.bitwise_or(combined_peaks, peaks)
        
        # Find connected components in combined peaks
        _, markers = cv2.connectedComponents(combined_peaks)
        
        # Add one to all labels so that background is not 0
        markers = markers + 1
        
        # Mark the unknown region (not stone centers) with 0
        markers[mask_copy == 0] = 0
        
        # Step 3: Apply watershed to separate stones
        # Convert mask to BGR for watershed
        mask_bgr = cv2.cvtColor(mask_copy, cv2.COLOR_GRAY2BGR)
        cv2.watershed(mask_bgr, markers)
        
        # Step 4: Extract each stone as a separate mask
        separated_masks = []
        for label in range(2, markers.max() + 1):  # Skip background (0) and unknown (1)
            # Create a mask for this stone
            stone_mask = np.zeros_like(mask_copy)
            stone_mask[markers == label] = 255
            
            # Only add if it has a reasonable size
            if np.sum(stone_mask > 0) > 100:
                # Clean up the mask with morphological operations
                kernel = np.ones((3, 3), np.uint8)
                stone_mask = cv2.morphologyEx(stone_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
                separated_masks.append(stone_mask)
        
        # If watershed didn't separate well, try additional methods
        if len(separated_masks) <= 1 and np.sum(mask_copy > 0) > 1000:
            # Method 2: Try separating using concavity analysis
            contours, _ = cv2.findContours(mask_copy, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                main_contour = max(contours, key=cv2.contourArea)
                
                # Find convex hull
                hull = cv2.convexHull(main_contour, returnPoints=False)
                
                # Find convexity defects
                try:
                    defects = cv2.convexityDefects(main_contour, hull)
                    
                    if defects is not None and len(defects) > 0:
                        # Sort defects by depth
                        defects = sorted(defects, key=lambda x: x[0][3], reverse=True)
                        
                        # Use the deepest defects to create separation lines
                        separation_mask = np.zeros_like(mask_copy)
                        
                        # Draw lines between significant defects and to the center
                        M = cv2.moments(main_contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            
                            for i in range(min(len(defects), 10)):  # Use more defects
                                s, e, f, d = defects[i][0]
                                start = tuple(main_contour[s][0])
                                end = tuple(main_contour[e][0])
                                far = tuple(main_contour[f][0])
                                
                                # Only use deep defects
                                if d / 256.0 > 5:  # Lower threshold to catch more defects
                                    # Draw line from defect point to contour center
                                    cv2.line(separation_mask, far, (cx, cy), 255, 2)
                                    
                                    # Also draw lines between nearby defect points
                                    for j in range(i+1, min(len(defects), 10)):
                                        s2, e2, f2, d2 = defects[j][0]
                                        far2 = tuple(main_contour[f2][0])
                                        
                                        # Calculate distance between defect points
                                        dist = np.sqrt((far[0]-far2[0])**2 + (far[1]-far2[1])**2)
                                        
                                        # If defects are close enough, connect them
                                        if dist < mask_copy.shape[0] * 0.5:  # Threshold based on image size
                                            cv2.line(separation_mask, far, far2, 255, 2)
                    
                    # Apply the separation lines to the original mask
                    separated = cv2.subtract(mask_copy, separation_mask)
                    
                    # Find connected components in the separated mask
                    num_labels, labels = cv2.connectedComponents(separated)
                    
                    # Create a mask for each component
                    for label in range(1, num_labels):
                        component_mask = np.zeros_like(mask_copy)
                        component_mask[labels == label] = 255
                        
                        # Only add if it has a reasonable size
                        if np.sum(component_mask > 0) > 100:
                            separated_masks.append(component_mask)
                except:
                    pass
            
            # Method 3: Try separating using morphological operations
            if len(separated_masks) <= 1:
                # Try erosion to separate touching stones
                kernel = np.ones((5, 5), np.uint8)
                eroded = cv2.erode(mask_copy, kernel, iterations=2)
                
                # Find connected components in eroded mask
                num_labels, labels = cv2.connectedComponents(eroded)
                
                if num_labels > 2:  # If we found multiple components
                    # Dilate each component separately to recover original shape
                    for label in range(1, num_labels):
                        component = np.zeros_like(mask_copy)
                        component[labels == label] = 255
                        
                        # Dilate to recover original shape, but don't let it grow beyond original mask
                        dilated = cv2.dilate(component, kernel, iterations=2)
                        dilated = cv2.bitwise_and(dilated, mask_copy)
                        
                        # Only add if it has a reasonable size
                        if np.sum(dilated > 0) > 100:
                            separated_masks.append(dilated)
        
        # If we still couldn't separate, return the original mask
        if len(separated_masks) == 0:
            separated_masks.append(mask_copy)
        
        return separated_masks

    def adjust_saturation_highlights(self, image, saturation_factor=-80, highlight_factor=80):
        """
        Adjust saturation and highlights of an image
        
        Parameters:
        - image: Input image (BGR format)
        - saturation_factor: Amount to adjust saturation (-100 to 100)
        - highlight_factor: Amount to adjust highlights (0 to 100)
        
        Returns:
        - Adjusted image
        """
        # Convert to HSV for saturation adjustment
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Adjust saturation
        h, s, v = cv2.split(hsv)
        
        # Scale saturation_factor to appropriate range (-100 to 100 -> -255 to 255)
        sat_adjust = int(saturation_factor * 2.55)
        
        # Apply saturation adjustment
        s = np.clip(s.astype(np.int16) + sat_adjust, 0, 255).astype(np.uint8)
        
        # Adjust highlights (increase brightness in brighter areas)
        highlight_threshold = 255 - highlight_factor
        highlight_mask = v > highlight_threshold
        
        # Apply highlight adjustment
        v_adjusted = v.copy()
        v_adjusted[highlight_mask] = np.clip(
            v_adjusted[highlight_mask] + (v_adjusted[highlight_mask] - highlight_threshold) * highlight_factor / 255, 
            0, 
            255
        ).astype(np.uint8)
        
        # Merge channels back
        hsv_adjusted = cv2.merge([h, s, v_adjusted])
        
        # Convert back to BGR
        adjusted = cv2.cvtColor(hsv_adjusted, cv2.COLOR_HSV2BGR)
        
        return adjusted

# Example usage
def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Stone Wall Generator')
    parser.add_argument('input_image', help='Path to the input stone wall image')
    parser.add_argument('output_image', help='Path to save the generated wall')
    parser.add_argument('--width', type=int, default=1200, help='Width of output wall in pixels')
    parser.add_argument('--height', type=int, default=800, help='Height of output wall in pixels')
    parser.add_argument('--gap', type=int, default=2, help='Gap between stones in pixels')
    parser.add_argument('--visualize', action='store_true', help='Visualize the stone detection process')
    parser.add_argument('--min-area', type=int, default=200, help='Minimum area for stone detection')
    parser.add_argument('--max-aspect', type=float, default=8.0, help='Maximum aspect ratio for stone detection')
    parser.add_argument('--bg-color', type=str, default='200,200,200', 
                        help='Background color as R,G,B (e.g., 200,200,200)')
    
    return parser.parse_args()

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    try:
        # Parse background color
        bg_color = tuple(map(int, args.bg_color.split(',')))
        if len(bg_color) != 3:
            raise ValueError("Background color must be in format R,G,B")
    except Exception as e:
        print(f"Error parsing background color: {e}")
        print("Using default background color (200,200,200)")
        bg_color = (200, 200, 200)
    
    try:
        # Create generator
        print(f"Loading image from {args.input_image}")
        generator = StoneWallGenerator(args.input_image)
        
        # Apply preprocessing and extract stones
        print("Applying image preprocessing and detecting stones...")
        print("This may take a moment as multiple methods are being evaluated...")
        
        generator.extract_stones(min_area=args.min_area, 
                                max_aspect_ratio=args.max_aspect,
                                visualize=args.visualize)
        
        if len(generator.stones) == 0:
            print("No stones were detected! Try adjusting the min-area and max-aspect parameters.")
            sys.exit(1)
        
        # Display extracted stones if visualization is enabled
        if args.visualize:
            print("Displaying extracted stones...")
            generator.display_extracted_stones(max_stones=30)  # Show more stones
        
        # Generate and save wall
        print(f"Generating wall ({args.width}x{args.height})...")
        generator.save_wall(args.output_image, 
                           width=args.width, 
                           height=args.height, 
                           bg_color=bg_color,
                           gap=args.gap)
        
        print(f"Stone wall successfully generated and saved to {args.output_image}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
