#!/usr/bin/env python3
"""
Simple test to create ashlar stone wall using synthetic rectangular stones
"""

import numpy as np
import random
from PIL import Image
import matplotlib.pyplot as plt

def create_synthetic_stone(width, height, base_color=(180, 170, 160)):
    """Create a synthetic rectangular stone"""
    stone_img = np.zeros((height, width, 4), dtype=np.uint8)
    
    # Add base color with variation
    for y in range(height):
        for x in range(width):
            color_var = random.randint(-15, 15)
            r = max(0, min(255, base_color[0] + color_var))
            g = max(0, min(255, base_color[1] + color_var))
            b = max(0, min(255, base_color[2] + color_var))
            stone_img[y, x] = [r, g, b, 255]
    
    # Add texture
    for _ in range(random.randint(2, 5)):
        if random.random() < 0.7:  # Horizontal lines
            y_line = random.randint(1, height-2)
            for x in range(width):
                if random.random() < 0.6:
                    stone_img[y_line, x, 0:3] = [max(0, c - 10) for c in stone_img[y_line, x, 0:3]]
        else:  # Vertical lines
            x_line = random.randint(1, width-2)
            for y in range(height):
                if random.random() < 0.6:
                    stone_img[y, x_line, 0:3] = [max(0, c - 10) for c in stone_img[y, x_line, 0:3]]
    
    # Add borders
    border_color = [max(0, c - 25) for c in base_color]
    for x in range(width):
        stone_img[0, x, 0:3] = border_color
        stone_img[height-1, x, 0:3] = border_color
    for y in range(height):
        stone_img[y, 0, 0:3] = border_color
        stone_img[y, width-1, 0:3] = border_color
    
    return stone_img

def create_stone_library():
    """Create a library of synthetic stones"""
    stones = []
    
    # Stone size categories
    stone_sizes = {
        'large': [(144, 64), (192, 80), (160, 72)],
        'medium': [(96, 48), (128, 64), (112, 56)],
        'small': [(64, 32), (48, 48), (80, 40)],
        'linear': [(240, 48), (192, 32), (200, 40)]
    }
    
    # Color variations
    colors = [
        (180, 170, 160),  # Light limestone
        (160, 150, 140),  # Medium limestone
        (140, 130, 120),  # Darker limestone
        (170, 160, 150),  # Warm limestone
    ]
    
    # Create stones for each category
    for category, sizes in stone_sizes.items():
        for width, height in sizes:
            for _ in range(3):  # Create 3 variations of each size
                color = random.choice(colors)
                stone_img = create_synthetic_stone(width, height, color)
                
                stone_info = {
                    'image': stone_img,
                    'width': width,
                    'height': height,
                    'area': width * height,
                    'category': category
                }
                stones.append(stone_info)
    
    return stones

def check_overlap(placed_stones, new_x, new_y, new_width, new_height):
    """Check if new stone overlaps with placed stones"""
    for stone in placed_stones:
        if (new_x < stone['x'] + stone['width'] and
            new_x + new_width > stone['x'] and
            new_y < stone['y'] + stone['height'] and
            new_y + new_height > stone['y']):
            return True
    return False

def generate_simple_ashlar_wall(width=1000, height=600):
    """Generate a simple ashlar stone wall"""
    print("Creating synthetic stone library...")
    stones = create_stone_library()
    print(f"Created {len(stones)} synthetic stones")
    
    # Create wall image
    wall_image = Image.new('RGB', (width, height), (220, 215, 210))
    placed_stones = []
    
    # Build from bottom to top
    current_y = height
    course_num = 0
    stones_placed = 0
    
    while current_y > 50:
        course_num += 1
        print(f"Building course {course_num}")
        
        # Select stones for this course
        if course_num <= 2:
            # Bottom courses: larger stones
            available_stones = [s for s in stones if s['category'] in ['large', 'linear']]
        else:
            # Upper courses: mix all sizes
            available_stones = stones.copy()
        
        # Determine course height
        course_height = max(s['height'] for s in available_stones[:5])
        course_height = min(course_height, current_y)
        course_y = current_y - course_height
        
        # Fill course left to right
        current_x = random.randint(0, 50)  # Random offset for joint staggering
        
        while current_x < width - 50:
            # Find suitable stones
            remaining_width = width - current_x
            suitable_stones = [s for s in available_stones 
                             if s['width'] <= remaining_width and s['height'] <= course_height + 10]
            
            if not suitable_stones:
                break
            
            # Select stone
            stone = random.choice(suitable_stones)
            stone_x = current_x
            stone_y = course_y + (course_height - stone['height']) // 2
            
            # Check for overlap
            if not check_overlap(placed_stones, stone_x, stone_y, stone['width'], stone['height']):
                # Place stone
                stone_pil = Image.fromarray(stone['image'])
                wall_image.paste(stone_pil, (stone_x, stone_y), stone_pil.split()[3])
                
                # Record placement
                placed_stones.append({
                    'x': stone_x,
                    'y': stone_y,
                    'width': stone['width'],
                    'height': stone['height']
                })
                stones_placed += 1
            
            current_x += stone['width'] + 2  # Small gap
        
        current_y = course_y
    
    print(f"Placed {stones_placed} stones in {course_num} courses")
    return np.array(wall_image)

def main():
    print("Simple Ashlar Stone Wall Test")
    print("=" * 40)
    
    # Generate wall
    wall_array = generate_simple_ashlar_wall(1200, 800)
    
    # Save wall
    wall_image = Image.fromarray(wall_array)
    output_path = "simple_ashlar_wall.png"
    wall_image.save(output_path)
    print(f"Wall saved to {output_path}")
    
    # Display wall
    plt.figure(figsize=(15, 10))
    plt.imshow(wall_array)
    plt.title("Simple Dry-Stack Ashlar Stone Wall", fontsize=16)
    plt.axis('off')
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    main()
