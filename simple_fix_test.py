#!/usr/bin/env python3
"""
Simple test to fix the ashlar wall generation with detected stones
"""

import sys
import os
import numpy as np
import random
from PIL import Image

# Import the updated stone wall generator
import importlib.util
spec = importlib.util.spec_from_file_location("stackStones4_Copy", "stackStones4 - Copy.py")
stackStones4_Copy = importlib.util.module_from_spec(spec)
spec.loader.exec_module(stackStones4_Copy)
StoneWallGenerator = stackStones4_Copy.StoneWallGenerator

def simple_ashlar_placement(stones, width=1000, height=800, bg_color=(220, 215, 210)):
    """Simple ashlar placement without complex logic"""
    
    print(f"Placing {len(stones)} stones in simple ashlar pattern...")
    
    # Create wall image
    wall_image = Image.new('RGB', (width, height), bg_color)
    
    # Track placed stones
    placed_stones = []
    stones_placed = 0
    
    # Sort stones by area (largest first) - use safe sorting
    try:
        stones.sort(key=lambda s: s.get('area', 0), reverse=True)
    except Exception as e:
        print(f"Warning: Could not sort stones by area: {e}")
        # Use stones in original order
        pass
    
    # Build from bottom up
    current_y = height
    course_num = 0
    
    while current_y > 50 and stones_placed < len(stones):
        course_num += 1
        print(f"Building course {course_num}")
        
        # Select stones for this course - use safer approach
        used_stone_ids = set(id(ps.get('stone_ref')) for ps in placed_stones if ps.get('stone_ref'))
        available_stones = [s for s in stones if id(s) not in used_stone_ids]
        if not available_stones:
            break
        
        # Determine course height
        course_height = min(80, max(s['height'] for s in available_stones[:10]))
        course_y = current_y - course_height
        
        # Fill course left to right
        current_x = random.randint(0, 50)  # Random offset
        
        while current_x < width - 50 and available_stones:
            # Find stones that fit
            remaining_width = width - current_x
            suitable_stones = []
            
            for stone in available_stones:
                # Get actual stone dimensions
                try:
                    stone_h, stone_w = stone['image'].shape[:2]
                    if stone_w <= remaining_width and stone_h <= course_height + 20:
                        suitable_stones.append(stone)
                except:
                    continue
            
            if not suitable_stones:
                break
            
            # Select stone
            stone = random.choice(suitable_stones)
            
            try:
                # Get stone image and dimensions
                stone_img = stone['image']
                stone_h, stone_w = stone_img.shape[:2]
                
                # Calculate position
                stone_x = current_x
                stone_y = course_y + (course_height - stone_h) // 2
                
                # Ensure stone doesn't go below bottom
                if stone_y + stone_h > height:
                    stone_y = height - stone_h
                
                # Check for overlaps
                overlap = False
                for placed_stone in placed_stones:
                    if (stone_x < placed_stone['x'] + placed_stone['width'] and
                        stone_x + stone_w > placed_stone['x'] and
                        stone_y < placed_stone['y'] + placed_stone['height'] and
                        stone_y + stone_h > placed_stone['y']):
                        overlap = True
                        break
                
                if not overlap:
                    # Convert stone to PIL and place
                    if stone_img.ndim == 4:
                        stone_pil = Image.fromarray(stone_img)
                    elif stone_img.ndim == 3:
                        stone_pil = Image.fromarray(stone_img)
                    else:
                        stone_pil = Image.fromarray(stone_img).convert('RGB')
                    
                    # Paste stone
                    if stone_img.ndim == 4:
                        wall_image.paste(stone_pil, (stone_x, stone_y), stone_pil)
                    else:
                        wall_image.paste(stone_pil, (stone_x, stone_y))
                    
                    # Record placement
                    placed_stones.append({
                        'x': stone_x,
                        'y': stone_y,
                        'width': stone_w,
                        'height': stone_h,
                        'stone_ref': stone
                    })
                    stones_placed += 1
                    
                    # Remove stone from available (safer approach)
                    try:
                        available_stones.remove(stone)
                    except ValueError:
                        pass  # Stone already removed
                
                # Move to next position
                current_x += stone_w + 2
                
            except Exception as e:
                print(f"Error placing stone: {e}")
                # Remove problematic stone
                try:
                    if stone in available_stones:
                        available_stones.remove(stone)
                except ValueError:
                    pass
                continue
        
        # Move to next course
        current_y = course_y
    
    print(f"Placed {stones_placed} stones in {course_num} courses")
    return np.array(wall_image)

def test_simple_fix():
    """Test the simple fix"""
    
    print("Testing Simple Ashlar Fix")
    print("=" * 40)
    
    # Test with stones1.jpg
    stone_image = 'stones1.jpg'
    if os.path.exists(stone_image):
        print(f"Testing with {stone_image}...")
        
        try:
            # Create generator and extract stones
            generator = StoneWallGenerator(stone_image)
            stones = generator.extract_stones(visualize=False)
            
            if stones:
                print(f"Extracted {len(stones)} stones")
                
                # Generate wall with simple placement
                wall_array = simple_ashlar_placement(stones, 1200, 800)
                
                # Save result
                wall_image = Image.fromarray(wall_array)
                output_path = "simple_ashlar_fix.png"
                wall_image.save(output_path)
                print(f"Wall saved to {output_path}")
                
                # Display if possible
                try:
                    import matplotlib.pyplot as plt
                    plt.figure(figsize=(15, 10))
                    plt.imshow(wall_array)
                    plt.title("Simple Ashlar Stone Wall (Fixed)", fontsize=16)
                    plt.axis('off')
                    plt.tight_layout()
                    plt.show()
                except ImportError:
                    print("Matplotlib not available")
                
            else:
                print("No stones extracted")
                
        except Exception as e:
            print(f"Error: {e}")
    else:
        print(f"Stone image {stone_image} not found")

if __name__ == "__main__":
    test_simple_fix()
